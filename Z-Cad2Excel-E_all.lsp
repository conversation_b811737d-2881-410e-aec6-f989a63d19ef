; ===================================================================
; CAD TO EXCEL - PHIEN BAN 2.0 TOI UU
; Tac gia: <PERSON>it <PERSON>
; Ngay phat hanh: 19/12/2024
; Phien ban: 2.0 Optimized
; ===================================================================
;
; HUONG DAN SU DUNG:
; 1. Load file: APPLOAD -> chon file .lsp
; 2. Su dung cac lenh theo nhom moi: E1x, E2x, E3x, E4x, E0, E7, E8, E9, ET
; 3. Cau hinh: Dung lenh E0 de thiet lap toan cuc (thay cho E6 cu)
; 4. Excel phai duoc mo truoc khi chay lenh
;
; DANH SACH LENH MOI:
; Nhom E1 - Xuat cell:
; E1  - Xuat text/dim vao cell (co he so factor) - giu nguyen
; E11 - Xuat text/dim voi he so nhan them (=(n1+n2+...)*a) - giu nguyen
; E12 - Xuat text/dim vao cell (text giu nguyen + dim co factor) - thay cho E2 cu
; E14 - Dat nhanh he so (factor)=1
; E15 - Dat nhanh he so (factor)=0.001
;
; Nhom E2 - Xuat Col, Row, Array:
; E2  - Xuat theo 3 che do (Col/Row/Array) voi thu tu selection - thay cho E3 cu
; E21 - Xuat nhanh ra cot (vi tri chuot tu dong nhay xuong o cuoi cung cua cot)
; E22 - Xuat nhanh ra hang (vi tri chuot tu dong nhay qua phai o cuoi cung cua hang)
; E23 - Xuat nhanh ra mang (vi tri chuot tu dong nhay xuong o cuoi cung cua mang)
; E24 - Bat nhanh Number (Y)
; E25 - Tat nhanh Number (N)
;
; Nhom E3 - Ghi Handle:
; E3  - Chi ghi handle vao comment - thay cho E4 cu
; E31 - Mo file Cad theo comment - thay cho HTC cu
; E32 - Zoom va highlight doi tuong theo handle - thay cho ZTH cu
; E33 - Zoom va select doi tuong theo handle - thay cho STH cu
; E34 - Bat nhanh handle (gan che do thiet lap handle Y)
; E35 - Tat nhanh handle (gan che do thiet lap handle N)
;
; Nhom E4 - Xuat Table:
; E4  - Xuat table vao Excel - thay cho E5 cu
; E41 - Xuat bang theo line (giu format bang) - thay cho CTE cu
; E42 - Xuat bang theo Table - thay cho TE cu
; E43 - Xuat bang tu excel qua cad - thay cho ETC cu
; E44 - Cong tac mo Frame (Y/N)
;
; E0  - Mo bang thiet lap - thay cho E6 cu
; E7  - Cong tac Handle (Toggle On/Off) - giu nguyen
; E8  - Cong tac Factor (Toggle 1/0.001) - giu nguyen
; E9  - Cong tac Number (Toggle Y/N) - giu nguyen
; ET  - Lenh tong hop toi uu - giu nguyen
;
; TINH NANG MOI:
; - Ho tro thu tu selection (click tung cai theo thu tu)
; - Chuyen doi ky hieu dac biet (%%c->Ø, %%p->±, %%d->°)
; - Xu ly MTEXT format codes
; - Tao cong thuc Excel khi symbol="+"
; - He so nhan them cho E11
;
; ===================================================================

; Bien toan cuc luu thiet lap
(or *E6-handle* (setq *E6-handle* "Y"))
(or *E6-frame* (setq *E6-frame* "N"))
(or *E6-symbol* (setq *E6-symbol* "+"))
(or *E6-factor* (setq *E6-factor* "1"))
(or *E6-jump* (setq *E6-jump* "3"))
(or *E6-tolerance* (setq *E6-tolerance* "50"))
(or *E6-number* (setq *E6-number* "N"))

; ===================================================================
; HAM PHU TRO CHUNG
; ===================================================================

; Ham tim ky tu trong chuoi (thay cho vl-string-search)
(defun FIND-CHAR ( char string / i found pos)
	(setq i 1 found nil pos nil)
	(while (and (<= i (strlen string)) (not found))
		(if (= (substr string i 1) char)
			(progn
				(setq found T)
				(setq pos (1- i))
			)
			(setq i (1+ i))
		)
	)
	pos
)

; Ham chuyen doi ky hieu dac biet trong text
(defun CONVERT-SPECIAL-SYMBOLS ( text-string / result)
	(if (and text-string (= (type text-string) 'STR) (> (strlen text-string) 0))
		(progn
			(setq result text-string)

			; Chuyen doi cac ky hieu dac biet
			(setq result (vl-string-subst "Ø" "%%c" result))  ; Diameter symbol
			(setq result (vl-string-subst "Ø" "%%C" result))  ; Diameter symbol (uppercase)
			(setq result (vl-string-subst "±" "%%p" result))  ; Plus-minus symbol
			(setq result (vl-string-subst "±" "%%P" result))  ; Plus-minus symbol (uppercase)
			(setq result (vl-string-subst "°" "%%d" result))  ; Degree symbol
			(setq result (vl-string-subst "°" "%%D" result))  ; Degree symbol (uppercase)

			result
		)
		""
	)
)

; Ham lam sach MTEXT - phien ban cai tien cho tieng Viet
(defun CLEAN-MTEXT ( mtext-string / result pos end-pos char-list i char new-result)
	(if (and mtext-string (= (type mtext-string) 'STR) (> (strlen mtext-string) 0))
		(progn
			(setq result mtext-string)

			; Buoc 1: Loai bo format \xxx; an toan hon
			(setq result (REMOVE-FORMAT-CODES result))

			; Buoc 2: Loai bo dau { va } nhung giu lai noi dung
			(setq result (REMOVE-BRACES result))

			; Buoc 3: Chuyen doi ky hieu dac biet
			(setq result (CONVERT-SPECIAL-SYMBOLS result))

			; Buoc 4: Trim khoang trang
			(setq result (TRIM-STRING result))

			result
		)
		""
	)
)

; Ham loai bo format codes an toan hon
(defun REMOVE-FORMAT-CODES ( text-string / result i char in-format format-start)
	(setq result "" i 1 in-format nil format-start 0)
	(while (<= i (strlen text-string))
		(setq char (substr text-string i 1))
		(cond
			; Bat dau format code
			((and (= char "\\") (not in-format))
				(setq in-format T format-start i)
			)
			; Ket thuc format code
			((and (= char ";") in-format)
				(setq in-format nil)
			)
			; Ky tu binh thuong (khong trong format code)
			((not in-format)
				(setq result (strcat result char))
			)
		)
		(setq i (+ i 1))
	)
	result
)

; Ham loai bo dau ngoac nhon an toan hon
(defun REMOVE-BRACES ( text-string / result i char brace-count)
	(setq result "" i 1 brace-count 0)
	(while (<= i (strlen text-string))
		(setq char (substr text-string i 1))
		(cond
			; Mo ngoac nhon
			((= char "{")
				(setq brace-count (+ brace-count 1))
			)
			; Dong ngoac nhon
			((= char "}")
				(setq brace-count (- brace-count 1))
			)
			; Ky tu binh thuong
			(T
				(setq result (strcat result char))
			)
		)
		(setq i (+ i 1))
	)
	result
)

; Ham trim string an toan
(defun TRIM-STRING ( text-string / result)
	(setq result text-string)
	; Loai bo khoang trang dau
	(while (and (> (strlen result) 0) (= (substr result 1 1) " "))
		(setq result (substr result 2))
	)
	; Loai bo khoang trang cuoi
	(while (and (> (strlen result) 0) (= (substr result (strlen result) 1) " "))
		(setq result (substr result 1 (1- (strlen result))))
	)
	result
)

; Bang chuyen doi Unicode escape sequences sang ky tu tieng Viet
(defun INIT-UNICODE-TABLE ( / unicode-table)
	(setq unicode-table (list
		; Cac ky tu thuong gap nhat
		(list "\\U+1ED4" "Ổ")  (list "\\U+1ED1" "ố")  ; O circumflex
		(list "\\U+1EE3" "ợ")  (list "\\U+01B0" "ư")  ; o horn, u horn
		(list "\\U+1EA3" "ả")  (list "\\U+1EA1" "ạ")  ; a hook, a dot
		(list "\\U+1EBB" "ẻ")  (list "\\U+1EB9" "ẹ")  ; e hook, e dot
		(list "\\U+1EC9" "ỉ")  (list "\\U+1ECB" "ị")  ; i hook, i dot
		(list "\\U+1ECD" "ọ")  (list "\\U+1ECF" "ỏ")  ; o dot, o hook
		(list "\\U+1EE5" "ụ")  (list "\\U+1EE7" "ủ")  ; u dot, u hook
		(list "\\U+1EF3" "ỳ")  (list "\\U+1EF5" "ỵ")  ; y grave, y dot
		(list "\\U+0110" "Đ")  (list "\\U+0111" "đ")  ; D stroke
		(list "\\U+01A0" "Ơ")  (list "\\U+01A1" "ơ")  ; O horn
		(list "\\U+01AF" "Ư")  (list "\\U+01B0" "ư")  ; U horn
		; Them cac ky tu khac neu can...
	))
	unicode-table
)

; Ham chuyen doi Unicode escape sequences
(defun CONVERT-UNICODE-ESCAPES (text-string / result unicode-table escape-seq vietnamese-char)
	(setq result text-string)
	(setq unicode-table (INIT-UNICODE-TABLE))

	; Duyet qua tung Unicode escape sequence trong bang
	(foreach unicode-pair unicode-table
		(setq escape-seq (nth 0 unicode-pair))
		(setq vietnamese-char (nth 1 unicode-pair))

		; Thay the tat ca cac escape sequence trong text
		(while (vl-string-search escape-seq result)
			(setq result (vl-string-subst vietnamese-char escape-seq result))
		)
	)

	result
)

; Ham xu ly text table co Unicode escape sequences - phien ban nang cao
(defun CLEAN-TABLE-TEXT ( text-string / result has-unicode)
	(if (and text-string (= (type text-string) 'STR) (> (strlen text-string) 0))
		(progn
			(setq result text-string)

			; Kiem tra xem co Unicode escape sequences khong
			(setq has-unicode (vl-string-search "\\U+" result))

			; Buoc 1: Xu ly Unicode escape sequences neu co
			(if has-unicode
				(setq result (CONVERT-UNICODE-ESCAPES result))
			)

			; Buoc 2: Xu ly format codes binh thuong neu co
			(if (or (vl-string-search "\\" result)
					(vl-string-search "{" result)
					(vl-string-search "}" result))
				(progn
					; Loai bo format codes
					(setq result (SIMPLE-REMOVE-FORMAT-CODES result))

					; Loai bo dau ngoac nhon
					(setq result (vl-string-subst "" "{" result))
					(setq result (vl-string-subst "" "}" result))
				)
			)

			; Buoc 3: Chuyen doi ky hieu dac biet
			(setq result (CONVERT-SPECIAL-SYMBOLS result))

			; Buoc 4: Trim va lam sach cuoi cung
			(setq result (TRIM-STRING result))

			result
		)
		""
	)
)

; Ham loai bo format codes DUNG - da hieu dung pattern
(defun SIMPLE-REMOVE-FORMAT-CODES (text-string / result start-pos end-pos after-semicolon)
	(setq result text-string)

	; Loai bo pattern: \[letter][number]; (co the co spaces sau semicolon)
	(while (setq start-pos (vl-string-search "\\" result))
		; Tim semicolon sau backslash
		(setq end-pos (vl-string-search ";" result start-pos))

		(if end-pos
			(progn
				; Tim vi tri sau semicolon va spaces
				(setq after-semicolon (+ end-pos 1))

				; Bo qua cac spaces sau semicolon
				(while (and (< after-semicolon (strlen result))
							(= (substr result (+ after-semicolon 1) 1) " "))
					(setq after-semicolon (+ after-semicolon 1))
				)

				; Loai bo tu backslash den het spaces
				(setq result (strcat
					(substr result 1 start-pos)
					(substr result (+ after-semicolon 1))
				))
			)
			; Neu khong co semicolon, loai bo het tu backslash
			(setq result (substr result 1 start-pos))
		)
	)

	result
)

; Ham ket noi Excel
(defun CONNECT-EXCEL ( / xlapp xlcells startrow startcol)
	(if (not (setq xlapp (vlax-get-object "Excel.Application")))
		(progn
			(princ "\nKhong tim thay Excel. Dang khoi dong Excel...")
			(setq xlapp (vlax-create-object "Excel.Application"))
		)
	)
	(vlax-put-property xlapp "Visible" :vlax-true)
	(setq xlcells (vlax-get-property (vlax-get-property xlapp "ActiveSheet") "Cells"))
	(setq startrow (vlax-get-property (vlax-get-property xlapp "ActiveCell") "Row"))
	(setq startcol (vlax-get-property (vlax-get-property xlapp "ActiveCell") "Column"))
	(list xlapp xlcells startrow startcol)
)

; Ham ghi cell text - cai tien
(defun SETCELLTEXT ( xlcells row col text / result cell worksheet range celladdress)
	; Thu cach 1: Su dung Item truc tiep
	(setq result (vl-catch-all-apply '(lambda ()
		(setq cell (vlax-get-property xlcells "Item" row col))
		(vlax-put-property cell "Value" text)
	)))

	; Neu cach 1 that bai, thu cach 2: Su dung Range
	(if (vl-catch-all-error-p result)
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER col) (itoa row)))
				(setq worksheet (vlax-get-property xlcells "Parent"))
				(setq range (vlax-get-property worksheet "Range" celladdress))
				(vlax-put-property range "Value" text)
			)))

			; Neu cach 2 cung that bai, thu cach 3: Su dung Cells truc tiep
			(if (vl-catch-all-error-p result)
				(progn
					(setq result (vl-catch-all-apply '(lambda ()
						(vlax-put-property xlcells "Item" row col text)
					)))
					(if (vl-catch-all-error-p result)
						(princ (strcat "\nLoi ghi cell: " (vl-catch-all-error-message result)))
					)
				)
			)
		)
	)
)

; Ham lay handle cua doi tuong
(defun GET-HANDLES ( ss / i handlelist ent)
	(setq handlelist '() i 0)
	(repeat (sslength ss)
		(setq ent (ssname ss i))
		(setq handlelist (cons (vla-get-handle (vlax-ename->vla-object ent)) handlelist))
		(setq i (+ i 1))
	)
	(reverse handlelist)
)

; Ham tao comment text
(defun CREATE-COMMENT-TEXT ( handlelist dwgpath dwgname / commenttext)
	(setq commenttext "")
	(foreach handle handlelist
		(setq commenttext (strcat commenttext handle "; "))
	)
	(setq commenttext (strcat commenttext "\nFileCad: " dwgpath))
	commenttext
)

; Ham ghi comment vao cell - sua loi VLA-OBJECT
(defun WRITE-COMMENT-TO-CELL ( cell commenttext / result comments comment xlapp worksheet celladdress)
	; Kiem tra cell co hop le khong
	(if (and cell (not (vl-catch-all-error-p cell)))
		(progn
			; Thu cach 1: Su dung cell truc tiep
			(setq result (vl-catch-all-apply '(lambda ()
				; Xoa comment cu neu co
				(if (vlax-property-available-p cell "Comment")
					(progn
						(setq comments (vlax-get-property cell "Comment"))
						(if comments
							(vlax-invoke-method comments "Delete")
						)
					)
				)
				; Tao comment moi
				(setq comment (vlax-invoke-method cell "AddComment" commenttext))
				(if comment
					(vlax-put-property comment "Visible" :vlax-false)
				)
			)))

			; Neu that bai, thu cach 2: Su dung Excel Application
			(if (vl-catch-all-error-p result)
				(progn
					(setq result (vl-catch-all-apply '(lambda ()
						(setq xlapp (vlax-get-acad-object))
						(setq xlapp (vlax-get-object "Excel.Application"))
						(setq worksheet (vlax-get-property xlapp "ActiveSheet"))
						(setq celladdress (vlax-get-property cell "Address"))
						(setq cell (vlax-get-property worksheet "Range" celladdress))
						(setq comment (vlax-invoke-method cell "AddComment" commenttext))
						(vlax-put-property comment "Visible" :vlax-false)
					)))

					; Neu van that bai, chi tao comment don gian
					(if (vl-catch-all-error-p result)
						(progn
							(setq result (vl-catch-all-apply '(lambda ()
								(vlax-invoke-method cell "AddComment" commenttext)
							)))
						)
					)
				)
			)
		)
		(princ "\nCell khong hop le!")
	)
)

; Ham chuyen so cot thanh chu cai
(defun COLUMN-NUMBER-TO-LETTER ( col / result)
	(setq result "")
	(while (> col 0)
		(setq col (- col 1))
		(setq result (strcat (chr (+ 65 (rem col 26))) result))
		(setq col (/ col 26))
	)
	result
)

; Ham trich xuat so tu text - co xu ly m2/m
(defun EXTRACT-NUMBER ( text-string / cleaned-text i char in-number current-number result has-unit)
	(if (and text-string (= (type text-string) 'STR) (> (strlen text-string) 0))
		(progn
			; Su dung ham CLEAN-MTEXT de lam sach text
			(setq cleaned-text (CLEAN-MTEXT text-string))

			; Kiem tra co don vi m2 hoac m khong
			(setq has-unit (or (vl-string-search "m2" cleaned-text) (vl-string-search "m" cleaned-text)))

			; Tim so trong chuoi
			(setq result "" i 1 in-number nil current-number "")
			(while (<= i (strlen cleaned-text))
				(setq char (substr cleaned-text i 1))
				(cond
					; Ky tu so
					((and (>= (ascii char) 48) (<= (ascii char) 57))
						(setq in-number T)
						(setq current-number (strcat current-number char))
					)
					; Dau thap phan
					((and in-number (member char '("." ",")))
						(setq current-number (strcat current-number char))
					)
					; Ky tu khac - ket thuc so
					(T
						(if (and in-number (> (strlen current-number) 0))
							(progn
								(setq result current-number)
								(setq i (strlen cleaned-text)) ; Thoat vong lap
							)
							(progn
								(setq in-number nil)
								(setq current-number "")
							)
						)
					)
				)
				(setq i (+ i 1))
			)

			; Neu ket thuc chuoi ma van dang trong so
			(if (and in-number (> (strlen current-number) 0))
				(setq result current-number)
			)

			; Chuyen doi thanh so
			(if (and result (> (strlen result) 0))
				(progn
					(setq result (vl-string-subst "." "," result))
					; Neu co don vi m2 hoac m, tra ve list (so, :unit) de nhan biet
					(if has-unit
						(list (atof result) :unit)
						(atof result)
					)
				)
				nil
			)
		)
		nil
	)
)

; Ham chon doi tuong thong minh - tu dong nhan dien cach chon
(defun SMART-SELECT-OBJECTS ( prompt filter / obj-list ent ss)
	(princ prompt)
	(princ "\n[Click tung doi tuong theo thu tu, hoac Window/Crossing de chon nhieu]")
	(setq obj-list '())

	; Thu chon tung doi tuong truoc
	(setq ent (entsel "\nChon doi tuong dau tien (hoac Enter de chon nhieu): "))

	(if ent
		(progn
			; Da chon 1 doi tuong, tiep tuc chon tung cai
			(setq ent (car ent))
			(if (or (not filter) (wcmatch (cdr (assoc 0 (entget ent))) filter))
				(progn
					(setq obj-list (list ent))
					(princ "\nDa chon 1 doi tuong. Tiep tuc chon tung cai...")

					; Chon tiep cac doi tuong khac
					(while (setq ent (entsel "\nChon doi tuong tiep theo (Enter de ket thuc): "))
						(if (= (type ent) 'LIST)
							(progn
								(setq ent (car ent))
								(if (or (not filter) (wcmatch (cdr (assoc 0 (entget ent))) filter))
									(progn
										(setq obj-list (append obj-list (list ent)))
										(princ (strcat "\nDa chon " (itoa (length obj-list)) " doi tuong"))
									)
									(princ "\nDoi tuong khong hop le!")
								)
							)
						)
					)

					; Tao selection set va luu thu tu
					(setq ss (ssadd))
					(foreach obj obj-list
						(ssadd obj ss)
					)
					(setq *SELECTION-ORDER* obj-list)
					ss
				)
				(progn
					(princ "\nDoi tuong khong hop le!")
					nil
				)
			)
		)
		; Khong chon tung cai, chuyen sang chon nhieu
		(progn
			(princ "\nChon nhieu doi tuong:")
			(setq *SELECTION-ORDER* nil)
			(ssget (if filter (list (cons 0 filter)) nil))
		)
	)
)

; Ham chuyen selection set thanh danh sach text (giu thu tu selection)
(defun CONVERT-SS-TO-TEXTLIST ( ss / i ent obj objname txtcontent txtpos measurement result raw-text)
	(setq result '())

	; Neu co thu tu selection da luu, su dung no
	(if (and *SELECTION-ORDER* (= (length *SELECTION-ORDER*) (sslength ss)))
		(progn
			; Su dung thu tu selection da luu
			(foreach ent *SELECTION-ORDER*
				(setq obj (vlax-ename->vla-object ent)
					  objname (vla-get-objectname obj)
					  txtcontent nil
					  txtpos nil)

				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring obj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring obj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
					; DIMENSION
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						(setq measurement (vla-get-measurement obj))
						(setq txtcontent (rtos measurement 2 2))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
				)

				(if txtcontent
					(setq result (append result (list (list txtcontent txtpos objname))))
				)
			)
		)
		; Neu khong co thu tu selection, su dung thu tu mac dinh
		(progn
			(setq i 0)
			(repeat (sslength ss)
				(setq ent (ssname ss i)
					  obj (vlax-ename->vla-object ent)
					  objname (vla-get-objectname obj)
					  txtcontent nil
					  txtpos nil)

				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring obj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring obj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
					; DIMENSION
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						(setq measurement (vla-get-measurement obj))
						(setq txtcontent (rtos measurement 2 2))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
				)

				(if txtcontent
					(setq result (cons (list txtcontent txtpos objname) result))
				)
				(setq i (+ i 1))
			)
			(setq result (reverse result))
		)
	)
	result
)

; Ham nhay chuot Excel - cai tien
(defun MOVE-CURSOR ( xlcells row col / targetcell result xlapp worksheet celladdress)
	; Thu cach 1: Su dung xlcells Item
	(setq result (vl-catch-all-apply '(lambda ()
		(setq targetcell (vlax-get-property xlcells "Item" row col))
		(vlax-invoke-method targetcell "Select")
	)))

	; Neu that bai, thu cach 2: Su dung Excel Application
	(if (vl-catch-all-error-p result)
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				(setq xlapp (vlax-get-object "Excel.Application"))
				(setq worksheet (vlax-get-property xlapp "ActiveSheet"))
				(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER col) (itoa row)))
				(setq targetcell (vlax-get-property worksheet "Range" celladdress))
				(vlax-invoke-method targetcell "Select")
			)))
		)
	)
)

; ===================================================================
; E1 - XUAT TEXT/DIM VAO CELL (THAY CHO E8 CU)
; ===================================================================

(defun C:E1 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	numlist
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))
			(setq numlist '())
			(setq factor (atof *E6-factor*))

			; Thu thap gia tri so
			(foreach txt textlist
				(setq objname (caddr txt))
				(cond
					; TEXT va MTEXT - trich xuat so
					((or (= objname "AcDbText") (= objname "AcDbMText"))
						(setq numvalue (EXTRACT-NUMBER (car txt)))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq numlist (cons (car numvalue) numlist))
									; Khong co don vi - nhan he so
									(setq numlist (cons (* numvalue factor) numlist))
								)
							)
						)
					)
					; DIMENSION - lay measurement
					((wcmatch objname "*Dimension*")
						(setq numvalue (atof (car txt)))
						(if numvalue
							(setq numlist (cons (* numvalue factor) numlist))
						)
					)
				)
			)

			(setq numlist (reverse numlist))

			; Xac dinh so chu so thap phan
			(setq decimal-places (if (= (atof *E6-factor*) 1.0) 0 3))

			; Tao noi dung cell
			(cond
				; 1 gia tri
				((= (length numlist) 1)
					(setq result-text (rtos (car numlist) 2 decimal-places))
				)
				; 2 gia tri
				((= (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel khi symbol la "+"
						(setq result-text (strcat "=" (rtos (car numlist) 2 decimal-places) "+" (rtos (cadr numlist) 2 decimal-places)))
						; Noi binh thuong voi symbol khac
						(setq result-text (strcat (rtos (car numlist) 2 decimal-places) *E6-symbol* (rtos (cadr numlist) 2 decimal-places)))
					)
				)
				; Nhieu hon 2 gia tri
				((> (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel khi symbol la "+"
						(progn
							(setq result-text (strcat "=" (rtos (car numlist) 2 decimal-places)))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text "+" (rtos num 2 decimal-places)))
							)
						)
						; Noi binh thuong voi symbol khac
						(progn
							(setq result-text (rtos (car numlist) 2 decimal-places))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text *E6-symbol* (rtos num 2 decimal-places)))
							)
						)
					)
				)
				; Khong co gia tri
				(T (setq result-text ""))
			)



			; Ghi vao Excel
			(if (and result-text (> (strlen result-text) 0))
				(progn
					(SETCELLTEXT xlcells startrow startcol result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							; Thu nhieu cach lay cell
							(setq result (vl-catch-all-apply '(lambda ()
								(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
							(if (vl-catch-all-error-p result)
								(progn
									; Thu cach khac
									(setq result (vl-catch-all-apply '(lambda ()
										(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
										(setq worksheet (vlax-get-property xlcells "Parent"))
										(setq targetcell (vlax-get-property worksheet "Range" celladdress))
										(WRITE-COMMENT-TO-CELL targetcell commenttext)
									)))
								)
							)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong co gia tri de ghi")
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; E11 - XUAT TEXT/DIM VAO CELL VOI HE SO NHAN THEM (TUONG TU E1)
; ===================================================================

(defun C:E11 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	numlist
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	multiplier
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Nhap he so nhan them
			(if (not *E11-multiplier*)
				(setq *E11-multiplier* "1")
			)
			(setq multiplier (getreal (strcat "\nNhap he so nhan them <" *E11-multiplier* ">: ")))
			(if (not multiplier)
				(setq multiplier (atof *E11-multiplier*))
				(setq *E11-multiplier* (rtos multiplier 2 6))
			)

			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))
			(setq numlist '())
			(setq factor (atof *E6-factor*))

			; Thu thap gia tri so
			(foreach txt textlist
				(setq objname (caddr txt))
				(cond
					; TEXT va MTEXT - trich xuat so
					((or (= objname "AcDbText") (= objname "AcDbMText"))
						(setq numvalue (EXTRACT-NUMBER (car txt)))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq numlist (cons (car numvalue) numlist))
									; Khong co don vi - nhan he so
									(setq numlist (cons (* numvalue factor) numlist))
								)
							)
						)
					)
					; DIMENSION - lay measurement
					((wcmatch objname "*Dimension*")
						(setq numvalue (atof (car txt)))
						(if numvalue
							(setq numlist (cons (* numvalue factor) numlist))
						)
					)
				)
			)

			(setq numlist (reverse numlist))

			; Xac dinh so chu so thap phan
			(setq decimal-places (if (= (atof *E6-factor*) 1.0) 0 3))

			; Tao noi dung cell voi he so nhan them
			(cond
				; 1 gia tri
				((= (length numlist) 1)
					(if (= multiplier 1.0)
						(setq result-text (rtos (car numlist) 2 decimal-places))
						(setq result-text (strcat "=" (rtos (car numlist) 2 decimal-places) "*" (rtos multiplier 2 6)))
					)
				)
				; 2 gia tri
				((= (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel khi symbol la "+"
						(if (= multiplier 1.0)
							(setq result-text (strcat "=" (rtos (car numlist) 2 decimal-places) "+" (rtos (cadr numlist) 2 decimal-places)))
							(setq result-text (strcat "=(" (rtos (car numlist) 2 decimal-places) "+" (rtos (cadr numlist) 2 decimal-places) ")*" (rtos multiplier 2 6)))
						)
						; Noi binh thuong voi symbol khac
						(if (= multiplier 1.0)
							(setq result-text (strcat (rtos (car numlist) 2 decimal-places) *E6-symbol* (rtos (cadr numlist) 2 decimal-places)))
							(setq result-text (strcat "=(" (rtos (car numlist) 2 decimal-places) *E6-symbol* (rtos (cadr numlist) 2 decimal-places) ")*" (rtos multiplier 2 6)))
						)
					)
				)
				; Nhieu hon 2 gia tri
				((> (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel khi symbol la "+"
						(progn
							(setq result-text (strcat "=(" (rtos (car numlist) 2 decimal-places)))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text "+" (rtos num 2 decimal-places)))
							)
							(if (= multiplier 1.0)
								(setq result-text (strcat result-text ")"))
								(setq result-text (strcat result-text ")*" (rtos multiplier 2 6)))
							)
						)
						; Noi binh thuong voi symbol khac
						(progn
							(setq result-text (strcat "=(" (rtos (car numlist) 2 decimal-places)))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text *E6-symbol* (rtos num 2 decimal-places)))
							)
							(if (= multiplier 1.0)
								(setq result-text (strcat result-text ")"))
								(setq result-text (strcat result-text ")*" (rtos multiplier 2 6)))
							)
						)
					)
				)
				; Khong co gia tri
				(T (setq result-text ""))
			)

			; Ghi vao Excel
			(if (and result-text (> (strlen result-text) 0))
				(progn
					(SETCELLTEXT xlcells startrow startcol result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							; Thu nhieu cach lay cell
							(setq result (vl-catch-all-apply '(lambda ()
								(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
							(if (vl-catch-all-error-p result)
								(progn
									; Thu cach khac
									(setq result (vl-catch-all-apply '(lambda ()
										(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
										(setq worksheet (vlax-get-property xlcells "Parent"))
										(setq targetcell (vlax-get-property worksheet "Range" celladdress))
										(WRITE-COMMENT-TO-CELL targetcell commenttext)
									)))
								)
							)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
					(princ (strcat "\nHe so nhan: " (rtos multiplier 2 6)))
				)
				(princ "\nKhong co gia tri de ghi")
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; E14 - DAT NHANH HE SO (FACTOR)=1
; ===================================================================

(defun C:E14 ( / )
	(setq *E6-factor* "1")
	(princ "\nDa dat he so factor = 1")
	(princ)
)

; ===================================================================
; E15 - DAT NHANH HE SO (FACTOR)=0.001
; ===================================================================

(defun C:E15 ( / )
	(setq *E6-factor* "0.001")
	(princ "\nDa dat he so factor = 0.001")
	(princ)
)

; ===================================================================
; E12 - XUAT TEXT/DIM VAO CELL (TEXT GIU NGUYEN + DIM CO FACTOR) - THAY CHO E2 CU
; ===================================================================

(defun C:E12 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	numlist
	textonly-list
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))
			(setq numlist '())
			(setq textonly-list '())
			(setq factor (atof *E6-factor*))

			; Phan loai text va dimension
			(foreach txt textlist
				(setq objname (caddr txt))
				(cond
					; TEXT va MTEXT - giu nguyen text
					((or (= objname "AcDbText") (= objname "AcDbMText"))
						(setq textonly-list (cons (car txt) textonly-list))
					)
					; DIMENSION - nhan voi he so
					((wcmatch objname "*Dimension*")
						(setq numvalue (atof (car txt)))
						(if numvalue
							(setq numlist (cons (* numvalue factor) numlist))
						)
					)
				)
			)

			(setq numlist (reverse numlist))
			(setq textonly-list (reverse textonly-list))

			; Tao noi dung cell - uu tien text truoc, sau do dimension
			(setq result-text "")

			; Xu ly text
			(if textonly-list
				(progn
					(setq result-text (car textonly-list))
					(foreach txt (cdr textonly-list)
						(setq result-text (strcat result-text *E6-symbol* txt))
					)
				)
			)

			; Xu ly dimension
			(if numlist
				(progn
					; Xac dinh so chu so thap phan cho dimension
					(setq decimal-places (if (= (atof *E6-factor*) 1.0) 0 3))

					(if (> (strlen result-text) 0)
						(setq result-text (strcat result-text *E6-symbol*))
					)

					(cond
						; 1 dimension
						((= (length numlist) 1)
							(setq result-text (strcat result-text (rtos (car numlist) 2 decimal-places)))
						)
						; Nhieu dimension
						((> (length numlist) 1)
							(if (= *E6-symbol* "+")
								; Tao cong thuc Excel cho dimension khi symbol la "+"
								(progn
									(setq dim-formula (strcat "=" (rtos (car numlist) 2 decimal-places)))
									(foreach num (cdr numlist)
										(setq dim-formula (strcat dim-formula "+" (rtos num 2 decimal-places)))
									)
									(setq result-text (strcat result-text dim-formula))
								)
								; Noi binh thuong voi symbol khac
								(progn
									(setq result-text (strcat result-text (rtos (car numlist) 2 decimal-places)))
									(foreach num (cdr numlist)
										(setq result-text (strcat result-text *E6-symbol* (rtos num 2 decimal-places)))
									)
								)
							)
						)
					)
				)
			)

			; Ghi vao Excel
			(if (and result-text (> (strlen result-text) 0))
				(progn
					(SETCELLTEXT xlcells startrow startcol result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							; Thu nhieu cach lay cell
							(setq result (vl-catch-all-apply '(lambda ()
								(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
							(if (vl-catch-all-error-p result)
								(progn
									; Thu cach khac
									(setq result (vl-catch-all-apply '(lambda ()
										(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
										(setq worksheet (vlax-get-property xlcells "Parent"))
										(setq targetcell (vlax-get-property worksheet "Range" celladdress))
										(WRITE-COMMENT-TO-CELL targetcell commenttext)
									)))
								)
							)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong co gia tri de ghi")
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; E4 - CHI GHI HANDLE VAO COMMENT (TRUOC DAY LA E3) - BAY GIO LA E3 MOI
; ===================================================================

(defun C:E3 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	handlelist
	commenttext
	dwgpath
	dwgname
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong
	(princ "\nChon doi tuong de ghi handle: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Lay handle va tao comment
			(setq handlelist (GET-HANDLES otcontents))
			(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
			; Thu nhieu cach lay cell
			(setq result (vl-catch-all-apply '(lambda ()
				(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
				(WRITE-COMMENT-TO-CELL targetcell commenttext)
			)))
			(if (vl-catch-all-error-p result)
				(progn
					; Thu cach khac
					(setq result (vl-catch-all-apply '(lambda ()
						(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
						(setq worksheet (vlax-get-property xlcells "Parent"))
						(setq targetcell (vlax-get-property worksheet "Range" celladdress))
						(WRITE-COMMENT-TO-CELL targetcell commenttext)
					)))
				)
			)

			; Nhay chuot
			(setq newrow (+ startrow (atoi *E6-jump*)))
			(MOVE-CURSOR xlcells newrow startcol)
			(princ (strcat "\nHoan thanh! Da ghi " (itoa (length handlelist)) " handle vao comment"))
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; E31 - MO FILE CAD THEO COMMENT (THAY CHO HTC CU)
; ===================================================================

(defun C:E31 ( / )
	; Goi ham HTC cu
	(C:HTC)
	(princ)
)

; ===================================================================
; E32 - ZOOM VA HIGHLIGHT DOI TUONG THEO HANDLE (THAY CHO ZTH CU)
; ===================================================================

(defun C:E32 ( / )
	; Goi ham ZTH cu
	(C:ZTH)
	(princ)
)

; ===================================================================
; E33 - ZOOM VA SELECT DOI TUONG THEO HANDLE (THAY CHO STH CU)
; ===================================================================

(defun C:E33 ( / )
	; Goi ham STH cu
	(C:STH)
	(princ)
)

; ===================================================================
; E34 - BAT NHANH HANDLE (GAN CHE DO THIET LAP HANDLE Y)
; ===================================================================

(defun C:E34 ( / )
	(setq *E6-handle* "Y")
	(princ "\nDa bat Handle = Y")
	(princ "\nCac lenh E1, E11, E12, E2, E3, E4, ET se ghi handle vao comment")
	(princ)
)

; ===================================================================
; E35 - TAT NHANH HANDLE (GAN CHE DO THIET LAP HANDLE N)
; ===================================================================

(defun C:E35 ( / )
	(setq *E6-handle* "N")
	(princ "\nDa tat Handle = N")
	(princ "\nCac lenh E1, E11, E12, E2, E3, E4, ET se KHONG ghi handle vao comment")
	(princ)
)

; ===================================================================
; E4 - XUAT TABLE VAO EXCEL (TRUOC DAY LA E5) - BAY GIO LA E4 MOI
; ===================================================================

(defun C:E4 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	lpxlist
	lpylist
	blocklist
	linelist
	colwidths
	rowheights
	newrow
	newcol
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon bang
	(princ "\nChon bang de xuat: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Khoi tao danh sach - giong E1 goc
			(setq textlist '() lpxlist '() lpylist '() blocklist '() linelist '())

			; Xu ly thong tin - dung logic E1 goc
			(E4-TABLEINFO otcontents)
			(setq lpxlist (vl-sort lpxlist '<) lpylist (vl-sort lpylist '>))
			(mapcar '(lambda (txt)(E4-GETTXTINFO (entget txt))) textlist)
			(mapcar '(lambda (blk)(E4-GETBLOCKINFO blk)) blocklist)
			(setq colwidths (mapcar '(lambda (x)(- (nth (1+ (vl-position x lpxlist)) lpxlist) x))(reverse (cdr (reverse lpxlist))))
				  rowheights (mapcar '(lambda (x)(- x (nth (1+ (vl-position x lpylist)) lpylist)))(reverse(cdr (reverse lpylist)))))

			; Xuat ra Excel
			(E4-EXPORT-TO-EXCEL xlcells startrow startcol)

			; Nhay chuot den cuoi bang
			(setq newrow (+ startrow (length lpylist) (atoi *E6-jump*)))
			(MOVE-CURSOR xlcells newrow startcol)
			(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
		)
		(princ "\nKhong chon duoc bang!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; HAM PHU TRO CHO E4
; ===================================================================

; Ham phan tich thong tin bang - logic E1 goc
(defun E4-TABLEINFO ( ss / n entlist)
	(setq n 0)
	(repeat (sslength ss)
		(setq entlist (entget (ssname ss n)))
		(cond
			((member (cdr (assoc 0 entlist)) '("LINE" "POLYLINE"))
				(E4-GETLINEPTS entlist)(setq linelist (cons (ssname ss n) linelist)))
			((member (cdr (assoc 0 entlist)) '("TEXT" "MTEXT"))
				(setq textlist (cons (ssname ss n) textlist)))
			((member (cdr (assoc 0 entlist)) '("INSERT"))
				(setq blocklist (cons (ssname ss n) blocklist)))
		)
		(setq n (1+ n))
	)
)

; Ham lay diem cua line va polyline - logic E1 goc
(defun E4-GETLINEPTS (alist / x xpt ypt)
	(foreach x alist
		(if (member (car x) '(10 11))
			(progn
				(if (not (vl-position (setq xpt (atof (rtos (car (trans (cdr x) 0 1)) 2 2))) lpxlist))
					(setq lpxlist (cons xpt lpxlist)))
				(if (not (vl-position (setq ypt (atof (rtos (cadr (trans (cdr x) 0 1)) 2 2))) lpylist))
					(setq lpylist (cons ypt lpylist)))
			)
		)
	)
)

; Ham lay thong tin text va vi tri cell - logic E1 goc chinh xac
(defun E4-GETTXTINFO (alist / x vlaobj pos rpos cpos expos txt)
	(setq txt (cdr (assoc -1 alist)))
	(setq vlaobj (vlax-ename->vla-object txt)
			pos (trans (E4-MIDP vlaobj) 0 1);Midpoint
			rpos (1- (vl-position (cadr pos)(vl-sort (cons (cadr pos) lpylist) '>)));Row Position
			cpos (1- (vl-position (car pos) (vl-sort (cons (car pos) lpxlist) '<))));Column Position
	(if (setq expos (vl-position (list rpos cpos) (mapcar '(lambda (x)(cdr (assoc "Position" x))) tinfo)));if cell is taken
		(setq tinfo
			(E4-REPLACE tinfo expos
				(E4-REPLACE
					(nth expos tinfo)
					2
					(cons "Content"
						(if (> (cadr pos) (cdr (assoc "Order" (nth expos tinfo))));in order according to y position
							(strcat (E4-VLA-FIELDCODE vlaobj) " " (cdr (assoc "Content" (nth expos tinfo))))
							(strcat (cdr (assoc "Content" (nth expos tinfo))) " " (E4-VLA-FIELDCODE vlaobj))
						)
					)
				)
			)
		)
		(setq tinfo
			(cons
				(list
					(cons "Order" (cadr pos))
					(cons "Position" (list rpos cpos));Position
					(cons "Content" (E4-VLA-FIELDCODE vlaobj));Content
				)
				tinfo
			)
		)
	)
)

; Ham lay thong tin block va vi tri cell - logic E1 goc chinh xac
(defun E4-GETBLOCKINFO (obj / pos rpos cpos bname objid bobj attid)
	(if (= (type obj) 'ename) (setq obj (vlax-ename->vla-object obj)))
	(setq pos (trans (E4-MIDP obj) 0 1)
		rpos (1- (vl-position (cadr pos) (vl-sort (cons (cadr pos) lpylist) '>)));Row Position
		cpos (1- (vl-position (car pos) (vl-sort (cons (car pos) lpxlist) '<)));Column Position
		bname (vla-get-name obj);Block Name
		bobj (vla-item (vla-get-blocks ActDoc) bname);Block Object
		attid '())
	(vlax-for i bobj
		(if (eq (vla-get-objectname i) "AcDbAttributeDefinition");If item is an attribute
			(setq attid (append attid (list (vla-get-objectid i))));List Attribute Id
		)
	)
	(setq objid (vla-get-objectid bobj));Block Object Id
	(setq binfo
		(cons
			(list
				(cons "Name" bname)
				(cons "Position" (list rpos cpos))
				(cons "ObjID" objid)
				(cons "Attributes"
					(mapcar
						'(lambda (x / attobj raw-text objname)
							(setq attobj (vla-objectidtoobject ActDoc x))
							(setq objname (vla-get-objectname attobj))
							(setq raw-text (vla-get-textstring attobj))
							; Neu la MTEXT, xu ly format text
							(if (= objname "AcDbMText")
								(cons (vla-get-tagstring attobj) (CLEAN-MTEXT raw-text))
								(cons (vla-get-tagstring attobj) raw-text)
							)
						)
						attid
					)
				)
				(cons "Scale" (vla-get-xscalefactor obj))
			)
			binfo
		)
	)
)

; Ham xuat bang ra Excel cho E4 - logic E1 goc chinh xac
(defun E4-EXPORT-TO-EXCEL ( xlcells startrow startcol / r c)
	; Khoi tao bien tinfo va binfo giong E1 goc
	(setq tinfo '() binfo '())

	; Xu ly text va block giong E1 goc
	(mapcar '(lambda (txt)(E4-GETTXTINFO (entget txt))) textlist)
	(mapcar '(lambda (blk)(E4-GETBLOCKINFO blk)) blocklist)

	; Dat text vao Excel tu vi tri con tro - giong E1 goc
	(mapcar '(lambda (x / r c)
			(setq r (+ startrow (cadr (assoc "Position" x)))
				  c (+ startcol (caddr (assoc "Position" x))))
			(SETCELLTEXT xlcells r c (cdr (assoc "Content" x)))
		)
		tinfo
	)

	; Dat thong tin block vao Excel tu vi tri con tro - giong E1 goc
	(mapcar '(lambda (x / r c bstring)
			(setq r (+ startrow (cadr (assoc "Position" x)))
				  c (+ startcol (caddr (assoc "Position" x))))
			(setq bstring "")
			(if (cdr (assoc "Attributes" x))
				(progn
				(mapcar
				'(lambda (y )
				(setq bstring (strcat ":"(cdr y) bstring)))
				(cdr (assoc "Attributes" x)))
				(SETCELLTEXT xlcells r c (strcat "Block:"(cdr (assoc "Name" x)) bstring))
				)
			)
		)
		binfo
	)

	; Dong khung neu can - sua lai kich thuoc
	(if (= *E6-frame* "Y")
		(E4-ADD-BORDERS xlcells startrow startcol (+ startrow (- (length lpylist) 1) -1) (+ startcol (- (length lpxlist) 1) -1))
	)
)

; Ham tim text tai vi tri - logic E1 goc
(defun E4-FIND-TEXT-AT-POSITION ( x y / best-txt min-dist txtitem txtpos dist)
	(setq best-txt nil min-dist 1e10)
	(foreach txtitem txtinfo
		(setq txtpos (cadr txtitem))
		(setq dist (sqrt (+ (expt (- x (car txtpos)) 2) (expt (- y (cadr txtpos)) 2))))
		(if (< dist min-dist)
			(progn
				(setq min-dist dist)
				(setq best-txt txtitem)
			)
		)
	)
	best-txt
)

; Ham ho tro - Midpoint cho E4
(defun E4-MIDP ( obj / )
	(vl-catch-all-apply '(lambda ()
		(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
	))
)

; Ham ho tro - VLA-FIELDCODE cho E4 (da sua loi MTEXT format)
(defun E4-VLA-FIELDCODE ( obj / raw-text objname)
	(setq objname (vla-get-objectname obj))
	(setq raw-text (vl-catch-all-apply '(lambda ()
		(vla-get-textstring obj)
	)))

	; Neu la MTEXT, xu ly format text
	(if (and (not (vl-catch-all-error-p raw-text)) (= objname "AcDbMText"))
		(CLEAN-MTEXT raw-text)
		raw-text
	)
)

; Ham ho tro - Replace cho E4
(defun E4-REPLACE ( lst index new-item / )
	(if (and (>= index 0) (< index (length lst)))
		(append (E4-SUBLIST lst 0 index) (list new-item) (E4-SUBLIST lst (+ index 1) (- (length lst) index 1)))
		lst
	)
)

; Ham ho tro - Sublist cho E4
(defun E4-SUBLIST ( lst start len / result i)
	(setq result '() i 0)
	(while (and (< i len) (< (+ start i) (length lst)))
		(setq result (append result (list (nth (+ start i) lst))))
		(setq i (+ i 1))
	)
	result
)

; Ham dong khung cho E4 - sua loi
(defun E4-ADD-BORDERS ( xlcells startrow startcol endrow endcol / range worksheet xlapp startaddr endaddr result)
	; Thu cach 1: Su dung Range truc tiep
	(setq result (vl-catch-all-apply '(lambda ()
		(setq startaddr (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
		(setq endaddr (strcat (COLUMN-NUMBER-TO-LETTER endcol) (itoa endrow)))
		(setq worksheet (vlax-get-property xlcells "Parent"))
		(setq range (vlax-get-property worksheet "Range" (strcat startaddr ":" endaddr)))
		(vlax-put-property (vlax-get-property range "Borders") "LineStyle" 1)
	)))

	; Neu that bai, thu cach 2: Su dung Excel Application
	(if (vl-catch-all-error-p result)
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				(setq xlapp (vlax-get-object "Excel.Application"))
				(setq worksheet (vlax-get-property xlapp "ActiveSheet"))
				(setq startaddr (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
				(setq endaddr (strcat (COLUMN-NUMBER-TO-LETTER endcol) (itoa endrow)))
				(setq range (vlax-get-property worksheet "Range" (strcat startaddr ":" endaddr)))
				; Dong tat ca cac vien
				(vlax-put-property (vlax-get-property range "Borders" 7) "LineStyle" 1)  ; xlEdgeLeft
				(vlax-put-property (vlax-get-property range "Borders" 8) "LineStyle" 1)  ; xlEdgeTop
				(vlax-put-property (vlax-get-property range "Borders" 9) "LineStyle" 1)  ; xlEdgeBottom
				(vlax-put-property (vlax-get-property range "Borders" 10) "LineStyle" 1) ; xlEdgeRight
				(vlax-put-property (vlax-get-property range "Borders" 11) "LineStyle" 1) ; xlInsideVertical
				(vlax-put-property (vlax-get-property range "Borders" 12) "LineStyle" 1) ; xlInsideHorizontal
			)))
		)
	)
)

; ===================================================================
; E41 - XUAT BANG THEO LINE (GIU FORMAT BANG) - THAY CHO CTE CU
; ===================================================================

(defun C:E41 ( / )
	; Goi ham CTE cu
	(C:CTE)
	(princ)
)

; ===================================================================
; E42 - XUAT BANG THEO TABLE - THAY CHO TE CU
; ===================================================================

(defun C:E42 ( / )
	; Goi ham TE cu
	(C:TE)
	(princ)
)

; ===================================================================
; E43 - XUAT BANG TU EXCEL QUA CAD - THAY CHO ETC CU
; ===================================================================

(defun C:E43 ( / )
	; Goi ham ETC cu
	(C:ETC)
	(princ)
)

; ===================================================================
; E44 - CONG TAC MO FRAME (Y/N)
; ===================================================================

(defun C:E44 ( / )
	; Dao trang thai Frame
	(if (= *E6-frame* "Y")
		(progn
			(setq *E6-frame* "N")
			(princ "\n=== FRAME: TAT ===")
			(princ "\nCac lenh E4, E41, E42 se KHONG dong khung cho bang")
		)
		(progn
			(setq *E6-frame* "Y")
			(princ "\n=== FRAME: MO ===")
			(princ "\nCac lenh E4, E41, E42 se dong khung cho bang")
		)
	)
	(princ (strcat "\nTrang thai hien tai: Frame = " *E6-frame*))
	(princ)
)

; ===================================================================
; E2 - XUAT THEO 3 CHE DO (TRUOC DAY LA E3) - BAY GIO LA E2 MOI
; ===================================================================

(defun C:E2 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	mode
	newrow
	newcol
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Chuyen doi selection set - su dung setting Number nhu cu
			(if (= *E6-number* "Y")
				(setq textlist (E5-CONVERT-SS-TO-TEXTLIST-NUMBER otcontents))
				(setq textlist (E5-CONVERT-SS-TO-TEXTLIST otcontents))
			)

			; Hien thi menu
			(princ "\nChon che do xuat:")
			(princ "\n1. Cot")
			(princ "\n2. Hang")
			(princ "\n3. Mang")
			(setq mode (getint "\nNhap lua chon [1-3]: "))

			(cond
				; Che do cot
				((= mode 1)
					(E5-EXPORT-COL textlist xlcells startrow startcol)
					; Nhay tu o cuoi cot xuong duoi
					(setq newrow (+ startrow (length textlist) (atoi *E6-jump*)) newcol startcol)
				)
				; Che do hang
				((= mode 2)
					(E5-EXPORT-ROW textlist xlcells startrow startcol)
					; Nhay tu o cuoi hang sang phai
					(setq newrow startrow newcol (+ startcol (length textlist) (atoi *E6-jump*)))
				)
				; Che do mang
				((= mode 3)
					(E5-EXPORT-ARRAY textlist xlcells startrow startcol)
					; Nhay tu dong cuoi bang xuong duoi
					(setq newrow (+ startrow (E5-GET-ARRAY-ROWS textlist) (atoi *E6-jump*)) newcol startcol)
				)
				; Lua chon khong hop le
				(T (princ "\nLua chon khong hop le!"))
			)

			; Ghi handle neu can
			(if (= *E6-handle* "Y")
				(progn
					(setq dwgpath (vla-get-fullname (vla-get-activedocument (vlax-get-acad-object))))
					(setq dwgname (vl-filename-base dwgpath))
					(setq handlelist (GET-HANDLES otcontents))
					(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
					; Thu nhieu cach lay cell
					(setq result (vl-catch-all-apply '(lambda ()
						(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
						(WRITE-COMMENT-TO-CELL targetcell commenttext)
					)))
					(if (vl-catch-all-error-p result)
						(progn
							; Thu cach khac
							(setq result (vl-catch-all-apply '(lambda ()
								(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
								(setq worksheet (vlax-get-property xlcells "Parent"))
								(setq targetcell (vlax-get-property worksheet "Range" celladdress))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
						)
					)
				)
			)

			; Nhay chuot
			(if (and newrow newcol)
				(progn
					(MOVE-CURSOR xlcells newrow newcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa newcol)))
				)
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; E21 - XUAT NHANH RA COT (VI TRI CHUOT TU DONG NHAY XUONG O CUOI CUNG CUA COT)
; ===================================================================

(defun C:E21 ( / )
	; Goi E2 voi mode = 1 (cot)
	(E2-EXECUTE-MODE 1)
	(princ)
)

; ===================================================================
; E22 - XUAT NHANH RA HANG (VI TRI CHUOT TU DONG NHAY QUA PHAI O CUOI CUNG CUA HANG)
; ===================================================================

(defun C:E22 ( / )
	; Goi E2 voi mode = 2 (hang)
	(E2-EXECUTE-MODE 2)
	(princ)
)

; ===================================================================
; E23 - XUAT NHANH RA MANG (VI TRI CHUOT TU DONG NHAY XUONG O CUOI CUNG CUA MANG)
; ===================================================================

(defun C:E23 ( / )
	; Goi E2 voi mode = 4 (mang)
	(E2-EXECUTE-MODE 4)
	(princ)
)

; ===================================================================
; E24 - BAT NHANH NUMBER (Y)
; ===================================================================

(defun C:E24 ( / )
	(setq *E6-number* "Y")
	(princ "\nDa bat Number = Y")
	(princ "\nE2 se chi lay so tu text (nhu E1)")
	(princ)
)

; ===================================================================
; E25 - TAT NHANH NUMBER (N)
; ===================================================================

(defun C:E25 ( / )
	(setq *E6-number* "N")
	(princ "\nDa tat Number = N")
	(princ "\nE2 se giu nguyen text nhu hien tai")
	(princ)
)

; ===================================================================
; HAM PHU TRO CHO E2 - THUC HIEN CHE DO NHANH
; ===================================================================

(defun E2-EXECUTE-MODE ( mode / ActDoc *Space* xlapp xlcells startrow startcol otcontents textlist newrow newcol oerror)
	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Chuyen doi selection set
			(setq textlist (E5-CONVERT-SS-TO-TEXTLIST otcontents))

			; Thuc hien theo mode
			(cond
				; Mode 1: Cot
				((= mode 1)
					(E5-EXECUTE-COL textlist xlcells startrow startcol)
					(setq newrow (+ startrow (length textlist) (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				; Mode 2: Hang
				((= mode 2)
					(E5-EXECUTE-ROW textlist xlcells startrow startcol)
					(setq newcol (+ startcol (length textlist) (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells startrow newcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa startrow) "," (itoa newcol)))
				)
				; Mode 4: Mang
				((= mode 4)
					(E5-EXECUTE-ARRAY textlist xlcells startrow startcol)
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
)

; ===================================================================
; HAM PHU TRO CHO E5
; ===================================================================

; Ham xuat theo cot - ho tro thu tu selection
(defun E5-EXPORT-COL ( textlist xlcells startrow startcol / sorted-textlist row tolerance xa ya xb yb)
	; Neu co thu tu selection, giu nguyen thu tu, neu khong thi sort
	(if (and *SELECTION-ORDER* (= (length *SELECTION-ORDER*) (length textlist)))
		; Giu nguyen thu tu selection
		(setq sorted-textlist textlist)
		; Sort theo toa do nhu cu
		(progn
			(setq tolerance (atof *E6-tolerance*))
			(setq sorted-textlist (vl-sort textlist '(lambda (a b)
				(setq xa (car (cadr a)) ya (cadr (cadr a))
					  xb (car (cadr b)) yb (cadr (cadr b)))
				; Neu cung cot (X gan bang nhau), sap xep theo Y (tren xuong)
				(if (< (abs (- xa xb)) tolerance)
					(> ya yb) ; Cung cot: tu tren xuong (Y giam dan)
					(< xa xb) ; Khac cot: tu trai sang phai (X tang dan)
				)
			)))
		)
	)
	(setq row startrow)
	(foreach txt sorted-textlist
		(SETCELLTEXT xlcells row startcol (car txt))
		(setq row (+ row 1))
	)
)

; Ham xuat theo hang - ho tro thu tu selection
(defun E5-EXPORT-ROW ( textlist xlcells startrow startcol / sorted-textlist col tolerance xa ya xb yb)
	; Neu co thu tu selection, giu nguyen thu tu, neu khong thi sort
	(if (and *SELECTION-ORDER* (= (length *SELECTION-ORDER*) (length textlist)))
		; Giu nguyen thu tu selection
		(setq sorted-textlist textlist)
		; Sort theo toa do nhu cu
		(progn
			(setq tolerance (atof *E6-tolerance*))
			(setq sorted-textlist (vl-sort textlist '(lambda (a b)
				(setq xa (car (cadr a)) ya (cadr (cadr a))
					  xb (car (cadr b)) yb (cadr (cadr b)))
				; Neu cung hang (Y gan bang nhau), sap xep theo X (trai phai)
				(if (< (abs (- ya yb)) tolerance)
					(< xa xb) ; Cung hang: tu trai sang phai (X tang dan)
					(> ya yb) ; Khac hang: tu tren xuong (Y giam dan)
				)
			)))
		)
	)
	(setq col startcol)
	(foreach txt sorted-textlist
		(SETCELLTEXT xlcells startrow col (car txt))
		(setq col (+ col 1))
	)
)

; Ham xuat theo mang
(defun E5-EXPORT-ARRAY ( textlist xlcells startrow startcol / unique-x unique-y tolerance grid-row grid-col excel-row excel-col)
	(setq tolerance (atof *E6-tolerance*))

	; Thu thap toa do X va Y
	(setq all-x '() all-y '())
	(foreach txt textlist
		(setq txt-pos (cadr txt))
		(setq all-x (cons (car txt-pos) all-x))
		(setq all-y (cons (cadr txt-pos) all-y))
	)

	; Tim cac toa do duy nhat
	(setq unique-x (E5-GET-UNIQUE-COORDS all-x tolerance))
	(setq unique-y (E5-GET-UNIQUE-COORDS all-y tolerance))
	(setq unique-x (vl-sort unique-x '<))
	(setq unique-y (vl-sort unique-y '>))

	; Xuat theo luoi
	(setq grid-row 0 excel-row startrow)
	(foreach y-coord unique-y
		(setq grid-col 0 excel-col startcol)
		(foreach x-coord unique-x
			(setq txt-at-pos (E5-FIND-TEXT-AT-GRID x-coord y-coord textlist tolerance))
			(if txt-at-pos
				(SETCELLTEXT xlcells excel-row excel-col (car txt-at-pos))
			)
			(setq grid-col (+ grid-col 1) excel-col (+ excel-col 1))
		)
		(setq grid-row (+ grid-row 1) excel-row (+ excel-row 1))
	)
)

; Ham lay so hang cua mang
(defun E5-GET-ARRAY-ROWS ( textlist / unique-y tolerance all-y)
	(setq tolerance (atof *E6-tolerance*))
	(setq all-y '())
	(foreach txt textlist
		(setq all-y (cons (cadr (cadr txt)) all-y))
	)
	(setq unique-y (E5-GET-UNIQUE-COORDS all-y tolerance))
	(length unique-y)
)

; Ham lay cac toa do duy nhat
(defun E5-GET-UNIQUE-COORDS ( coord-list tolerance / unique-coords coord)
	(setq unique-coords '())
	(foreach coord coord-list
		(if (not (E5-COORD-EXISTS coord unique-coords tolerance))
			(setq unique-coords (cons coord unique-coords))
		)
	)
	unique-coords
)

; Ham kiem tra toa do da ton tai
(defun E5-COORD-EXISTS ( coord coord-list tolerance / exists)
	(setq exists nil)
	(foreach existing-coord coord-list
		(if (< (abs (- coord existing-coord)) tolerance)
			(setq exists T)
		)
	)
	exists
)

; Ham tim text tai vi tri luoi
(defun E5-FIND-TEXT-AT-GRID ( x y textlist tolerance / best-txt min-dist txt txt-pos dist)
	(setq best-txt nil min-dist 1e10)
	(foreach txt textlist
		(setq txt-pos (cadr txt))
		(setq dist (sqrt (+ (expt (- x (car txt-pos)) 2) (expt (- y (cadr txt-pos)) 2))))
		(if (and (< dist min-dist) (< dist tolerance))
			(progn
				(setq min-dist dist)
				(setq best-txt txt)
			)
		)
	)
	best-txt
)

; ===================================================================
; E0 - SETTINGS (THIET LAP TOAN CUC) - GIAO DIEN DCL - THAY CHO E6 CU
; ===================================================================

(defun C:E0 ( / DCH DCL DIALOG FLAG TITLE)
	(setq title "Thiet lap CAD to Excel v2024")
	(setq dialog "E6SETTINGS")
	(setq dcl (vl-filename-mktemp nil nil ".dcl"))

	(cond
		((not (E6-WRITE-DCL dcl dialog title))
			(princ "\nKhong the tao file DCL.")
		)
		((<= (setq dch (load_dialog dcl)) 0)
			(princ "\nKhong the load file DCL.")
		)
		(t
			(if (not (new_dialog dialog dch))
				(progn
					(princ "\nKhong the tao dialog.")
					(exit)
				)
			)

			; Thiet lap gia tri ban dau
			(E6-SET-TILES)

			; Gan action cho cac control
			(action_tile "E6-HANDLE" "(E6-UPDATE-PREVIEW)")
			(action_tile "E6-FRAME" "(E6-UPDATE-PREVIEW)")
			(action_tile "E6-NUMBER" "(E6-UPDATE-PREVIEW)")
			(action_tile "E6-SYMBOL" "(E6-UPDATE-PREVIEW)")
			(action_tile "E6-FACTOR" "(E6-UPDATE-PREVIEW)")
			(action_tile "E6-JUMP" "(E6-UPDATE-PREVIEW)")
			(action_tile "E6-TOLERANCE" "(E6-UPDATE-PREVIEW)")
			(action_tile "accept" "(E6-SAVE-SETTINGS)")
			(action_tile "cancel" "(done_dialog)")
			(action_tile "E6-RESET" "(E6-RESET-SETTINGS)")

			; Cap nhat preview ban dau
			(E6-UPDATE-PREVIEW)

			; Hien thi dialog
			(start_dialog)
			(unload_dialog dch)
		)
	)
	(princ)
)

; ===================================================================
; HAM PHU TRO CHO E6 DCL
; ===================================================================

; Ham tao file DCL cho E6
(defun E6-WRITE-DCL ( dcl dialog title / des lst)
	(setq lst
		(list
			dialog
			": dialog"
			"{"
			(strcat "\tlabel = \"" title "\";")
			"\t: column"
			"\t\t{"
			"\t\t: boxed_row"
			"\t\t\t{"
			"\t\t\tlabel = \"Tuy chon chinh\";"
			"\t\t\t: boxed_column"
			"\t\t\t\t{"
			"\t\t\t\tlabel = \"Ghi du lieu\";"
			"\t\t\t\t: toggle { key = \"E6-HANDLE\"; label = \"Ghi Handle vao Comment\"; }"
			"\t\t\t\t: toggle { key = \"E6-FRAME\"; label = \"Dong khung cho bang\"; }"
			"\t\t\t\t: toggle { key = \"E6-NUMBER\"; label = \"E3 chi lay so (nhu E1)\"; }"
			"\t\t\t\t}"
			"\t\t\t: boxed_column"
			"\t\t\t\t{"
			"\t\t\t\tlabel = \"Thong so\";"
			"\t\t\t\t: edit_box { key = \"E6-SYMBOL\"; label = \"Ky tu noi\"; edit_width = 8; }"
			"\t\t\t\t: edit_box { key = \"E6-FACTOR\"; label = \"He so\"; edit_width = 8; }"
			"\t\t\t\t: edit_box { key = \"E6-JUMP\"; label = \"So o nhay\"; edit_width = 8; }"
			"\t\t\t\t: edit_box { key = \"E6-TOLERANCE\"; label = \"Sai so cho phep\"; edit_width = 8; }"
			"\t\t\t\t}"
			"\t\t\t}"
			"\t\t: boxed_column"
			"\t\t\t{"
			"\t\t\tlabel = \"Xem truoc\";"
			"\t\t\t: text { key = \"E6-PREVIEW\"; label = \"Thiet lap hien tai:\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-HANDLE\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-FRAME\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-SYMBOL\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-FACTOR\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-JUMP\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-TOLERANCE\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-NUMBER\"; }"
			"\t\t\t}"
			"\t\t: boxed_column"
			"\t\t\t{"
			"\t\t\tlabel = \"Huong dan\";"
			"\t\t\t: text { value = \"Handle: Ghi ma doi tuong vao comment Excel\"; }"
			"\t\t\t: text { value = \"Frame: Dong khung cho bang (E5)\"; }"
			"\t\t\t: text { value = \"Symbol: Ky tu noi cac gia tri\"; }"
			"\t\t\t: text { value = \"  '+' se tao cong thuc Excel\"; }"
			"\t\t\t: text { value = \"Factor: He so nhan (1=khong doi, 0.001=mm->m)\"; }"
			"\t\t\t: text { value = \"Jump: So o nhay sau khi ghi\"; }"
			"\t\t\t: text { value = \"Tolerance: Sai so cho phep (pixel)\"; }"
			"\t\t\t: text { value = \"Number: E3 chi lay so, nhan he so\"; }"
			"\t\t\t}"
			"\t\t: row"
			"\t\t\t{"
			"\t\t\t: spacer { width = 1; }"
			"\t\t\t: button { key = \"E6-RESET\"; label = \"Mac dinh\"; fixed_width = true; width = 12; }"
			"\t\t\t: button { key = \"accept\"; label = \"OK\"; is_default = true; fixed_width = true; width = 12; }"
			"\t\t\t: button { key = \"cancel\"; label = \"Huy\"; is_cancel = true; fixed_width = true; width = 12; }"
			"\t\t\t: spacer { width = 1; }"
			"\t\t\t}"
			"\t\t}"
			"}"
		)
	)
	(setq des (open dcl "w"))
	(foreach x lst (write-line x des))
	(setq des (close des))
	(while (not (findfile dcl)))
	dcl
)

; Ham thiet lap gia tri ban dau cho cac tile
(defun E6-SET-TILES ( / )
	; Thiet lap checkbox
	(set_tile "E6-HANDLE" (if (= *E6-handle* "Y") "1" "0"))
	(set_tile "E6-FRAME" (if (= *E6-frame* "Y") "1" "0"))
	(set_tile "E6-NUMBER" (if (= *E6-number* "Y") "1" "0"))

	; Thiet lap edit box
	(set_tile "E6-SYMBOL" *E6-symbol*)
	(set_tile "E6-FACTOR" *E6-factor*)
	(set_tile "E6-JUMP" *E6-jump*)
	(set_tile "E6-TOLERANCE" *E6-tolerance*)
)

; Ham cap nhat preview
(defun E6-UPDATE-PREVIEW ( / handle-val frame-val number-val symbol-val factor-val jump-val tolerance-val)
	; Lay gia tri hien tai tu dialog
	(setq handle-val (if (= (get_tile "E6-HANDLE") "1") "Y" "N"))
	(setq frame-val (if (= (get_tile "E6-FRAME") "1") "Y" "N"))
	(setq number-val (if (= (get_tile "E6-NUMBER") "1") "Y" "N"))
	(setq symbol-val (get_tile "E6-SYMBOL"))
	(setq factor-val (get_tile "E6-FACTOR"))
	(setq jump-val (get_tile "E6-JUMP"))
	(setq tolerance-val (get_tile "E6-TOLERANCE"))

	; Cap nhat preview
	(set_tile "E6-PREVIEW-HANDLE" (strcat "Handle: " handle-val))
	(set_tile "E6-PREVIEW-FRAME" (strcat "Frame: " frame-val))
	(set_tile "E6-PREVIEW-SYMBOL" (strcat "Symbol: " symbol-val))
	(set_tile "E6-PREVIEW-FACTOR" (strcat "Factor: " factor-val))
	(set_tile "E6-PREVIEW-JUMP" (strcat "Jump: " jump-val))
	(set_tile "E6-PREVIEW-TOLERANCE" (strcat "Tolerance: " tolerance-val))
	(set_tile "E6-PREVIEW-NUMBER" (strcat "Number: " number-val))
)

; Ham luu thiet lap
(defun E6-SAVE-SETTINGS ( / )
	; Validate du lieu
	(cond
		((= (get_tile "E6-SYMBOL") "")
			(alert "Ky tu noi khong duoc de trong!")
			(mode_tile "E6-SYMBOL" 2)
		)
		((<= (atof (get_tile "E6-FACTOR")) 0)
			(alert "He so phai lon hon 0!")
			(mode_tile "E6-FACTOR" 2)
		)
		((<= (atoi (get_tile "E6-JUMP")) 0)
			(alert "So o nhay phai lon hon 0!")
			(mode_tile "E6-JUMP" 2)
		)
		((<= (atof (get_tile "E6-TOLERANCE")) 0)
			(alert "Sai so cho phep phai lon hon 0!")
			(mode_tile "E6-TOLERANCE" 2)
		)
		(t
			; Luu cac gia tri
			(setq *E6-handle* (if (= (get_tile "E6-HANDLE") "1") "Y" "N"))
			(setq *E6-frame* (if (= (get_tile "E6-FRAME") "1") "Y" "N"))
			(setq *E6-number* (if (= (get_tile "E6-NUMBER") "1") "Y" "N"))
			(setq *E6-symbol* (get_tile "E6-SYMBOL"))
			(setq *E6-factor* (get_tile "E6-FACTOR"))
			(setq *E6-jump* (get_tile "E6-JUMP"))
			(setq *E6-tolerance* (get_tile "E6-TOLERANCE"))

			(princ "\n=== DA LUU THIET LAP ===")
			(princ (strcat "\nHandle: " *E6-handle*))
			(princ (strcat "\nFrame: " *E6-frame*))
			(princ (strcat "\nSymbol: " *E6-symbol*))
			(princ (strcat "\nFactor: " *E6-factor*))
			(princ (strcat "\nJump: " *E6-jump*))
			(princ (strcat "\nTolerance: " *E6-tolerance*))
			(princ (strcat "\nNumber: " *E6-number*))

			; Dong dialog sau khi luu
			(done_dialog)
		)
	)
)

; Ham reset ve gia tri mac dinh
(defun E6-RESET-SETTINGS ( / )
	; Reset ve gia tri mac dinh
	(setq *E6-handle* "Y")
	(setq *E6-frame* "N")
	(setq *E6-symbol* "+")
	(setq *E6-factor* "1")
	(setq *E6-jump* "3")
	(setq *E6-tolerance* "50")
	(setq *E6-number* "N")

	; Cap nhat lai dialog
	(E6-SET-TILES)
	(E6-UPDATE-PREVIEW)

	(princ "\nDa reset ve gia tri mac dinh!")
)

; ===================================================================
; E7 - CONG TAC HANDLE (TOGGLE ON/OFF)
; ===================================================================

(defun C:E7 ( / )
	; Dao trang thai Handle
	(if (= *E6-handle* "Y")
		(progn
			(setq *E6-handle* "N")
			(princ "\n=== HANDLE: TAT ===")
			(princ "\nCac lenh E1, E11, E12, E2, E3, E4, ET se KHONG ghi handle vao comment")
		)
		(progn
			(setq *E6-handle* "Y")
			(princ "\n=== HANDLE: MO ===")
			(princ "\nCac lenh E1, E11, E12, E2, E3, E4, ET se ghi handle vao comment")
		)
	)
	(princ (strcat "\nTrang thai hien tai: Handle = " *E6-handle*))
	(princ)
)

; ===================================================================
; E8 - CONG TAC FACTOR (TOGGLE 1 <-> 0.001)
; ===================================================================

(defun C:E8 ( / )
	; Dao giua 1 va 0.001
	(if (= *E6-factor* "1")
		(progn
			(setq *E6-factor* "0.001")
			(princ "\n=== FACTOR: 0.001 (mm -> m) ===")
			(princ "\nCac lenh E1, E11, E12, E2, ET se nhan gia tri voi 0.001")
			(princ "\nDung de chuyen doi tu mm sang m")
		)
		(progn
			(setq *E6-factor* "1")
			(princ "\n=== FACTOR: 1 (khong doi) ===")
			(princ "\nCac lenh E1, E11, E12, E2, ET se giu nguyen gia tri")
			(princ "\nKhong nhan he so")
		)
	)
	(princ (strcat "\nTrang thai hien tai: Factor = " *E6-factor*))
	(princ)
)

; ===================================================================
; E9 - CONG TAC NUMBER (TOGGLE Y/N)
; ===================================================================

(defun C:E9 ( / )
	; Dao trang thai Number
	(if (= *E6-number* "Y")
		(progn
			(setq *E6-number* "N")
			(princ "\n=== NUMBER: TAT ===")
			(princ "\nE2 se giu nguyen text nhu hien tai")
			(princ "\nKhong trich xuat so, khong nhan he so")
		)
		(progn
			(setq *E6-number* "Y")
			(princ "\n=== NUMBER: MO ===")
			(princ "\nE2 se chi lay so tu text (nhu E1)")
			(princ "\nTrich xuat so, nhan he so, bo qua hau to m2/m")
		)
	)
	(princ (strcat "\nTrang thai hien tai: Number = " *E6-number*))
	(princ)
)

; ===================================================================
; ET - LENH TONG HOP TOI UU
; ===================================================================

(defun C:ET ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	mode
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong
	(princ "\nChon doi tuong: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Hien thi menu chinh
			(princ "\n=== CAD TO EXCEL ===")
			(princ "\nChon che do:")
			(princ "\n1. Table - Xuat bang")
			(princ "\n2. Col - Xuat theo cot")
			(princ "\n3. Row - Xuat theo hang")
			(princ "\n4. Cell - Xuat vao 1 o")
			(princ "\n5. Array - Xuat theo mang")
			(princ "\n6. Handle - Chi ghi handle")
			(princ "\n7. Value - Ghi gia tri + handle")
			(setq mode (getint "\nNhap lua chon [1-7]: "))

			(cond
				; Table mode
				((= mode 1) (ET-EXECUTE-TABLE otcontents xlcells startrow startcol))
				; Col mode
				((= mode 2) (ET-EXECUTE-COL otcontents xlcells startrow startcol))
				; Row mode
				((= mode 3) (ET-EXECUTE-ROW otcontents xlcells startrow startcol))
				; Cell mode
				((= mode 4) (ET-EXECUTE-CELL otcontents xlcells startrow startcol))
				; Array mode
				((= mode 5) (ET-EXECUTE-ARRAY otcontents xlcells startrow startcol))
				; Handle mode
				((= mode 6) (ET-EXECUTE-HANDLE otcontents xlcells startrow startcol))
				; Value mode
				((= mode 7) (ET-EXECUTE-VALUE otcontents xlcells startrow startcol))
				; Lua chon khong hop le
				(T (princ "\nLua chon khong hop le!"))
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; HAM PHU TRO CHUNG - E5-CONVERT-SS-TO-TEXTLIST
; ===================================================================

; Ham chuyen selection set thanh list text cho E5 (ho tro dimension)
(defun E5-CONVERT-SS-TO-TEXTLIST ( ss / n ent textlist txtobj txtcontent txtpos objname measurement)
	(setq textlist '())

	; Neu co thu tu selection da luu, su dung no
	(if (and *SELECTION-ORDER* (= (length *SELECTION-ORDER*) (sslength ss)))
		(progn
			; Su dung thu tu selection da luu
			(foreach ent *SELECTION-ORDER*
				(setq txtobj (vlax-ename->vla-object ent))
				(setq objname (vla-get-objectname txtobj))

				; Xu ly theo loai doi tuong
				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; DIMENSION (tat ca cac loai)
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						; Lay gia tri Measurement va lam tron 3 chu so thap phan
						(setq measurement (vla-get-measurement txtobj))
						(setq txtcontent (rtos measurement 2 3)) ; Lam tron 3 chu so thap phan
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; Loai khac - bo qua
					(T
						(setq txtcontent nil txtpos nil)
					)
				)

				; Them vao list neu co noi dung
				(if (and txtcontent txtpos)
					(setq textlist (append textlist (list (list txtcontent txtpos))))
				)
			)
		)
		; Neu khong co thu tu selection, su dung thu tu mac dinh
		(progn
			(setq n 0)
			(repeat (sslength ss)
				(setq ent (ssname ss n))
				(setq txtobj (vlax-ename->vla-object ent))
				(setq objname (vla-get-objectname txtobj))

				; Xu ly theo loai doi tuong
				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; DIMENSION (tat ca cac loai)
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						; Lay gia tri Measurement va lam tron 3 chu so thap phan
						(setq measurement (vla-get-measurement txtobj))
						(setq txtcontent (rtos measurement 2 3)) ; Lam tron 3 chu so thap phan
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; Loai khac - bo qua
					(T
						(setq txtcontent nil txtpos nil)
					)
				)

				; Them vao list neu co noi dung
				(if (and txtcontent txtpos)
					(setq textlist (append textlist (list (list txtcontent txtpos))))
				)
				(setq n (+ n 1))
			)
		)
	)
	textlist
)

; Ham chuyen selection set thanh list text cho E5 - che do Number (chi lay so)
(defun E5-CONVERT-SS-TO-TEXTLIST-NUMBER ( ss / n ent textlist txtobj txtcontent txtpos objname measurement numvalue factor decimal-places)
	(setq textlist '())
	(setq factor (atof *E6-factor*))
	(setq decimal-places (if (= factor 1.0) 0 3))

	; Neu co thu tu selection da luu, su dung no
	(if (and *SELECTION-ORDER* (= (length *SELECTION-ORDER*) (sslength ss)))
		(progn
			; Su dung thu tu selection da luu
			(foreach ent *SELECTION-ORDER*
				(setq txtobj (vlax-ename->vla-object ent))
				(setq objname (vla-get-objectname txtobj))

				; Xu ly theo loai doi tuong
				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
						; Trich xuat so va nhan he so
						(setq numvalue (EXTRACT-NUMBER txtcontent))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq txtcontent (rtos (car numvalue) 2 decimal-places))
									; Khong co don vi - nhan he so
									(setq txtcontent (rtos (* numvalue factor) 2 decimal-places))
								)
							)
							; Neu khong trich xuat duoc so, giu nguyen text
							(setq txtcontent txtcontent)
						)
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
						; Trich xuat so va nhan he so
						(setq numvalue (EXTRACT-NUMBER txtcontent))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq txtcontent (rtos (car numvalue) 2 decimal-places))
									; Khong co don vi - nhan he so
									(setq txtcontent (rtos (* numvalue factor) 2 decimal-places))
								)
							)
							; Neu khong trich xuat duoc so, giu nguyen text
							(setq txtcontent txtcontent)
						)
					)
					; DIMENSION (tat ca cac loai)
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						; Lay gia tri Measurement, nhan he so va lam tron
						(setq measurement (vla-get-measurement txtobj))
						(setq txtcontent (rtos (* measurement factor) 2 decimal-places))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; Loai khac - bo qua
					(T
						(setq txtcontent nil txtpos nil)
					)
				)

				; Them vao list neu co noi dung
				(if (and txtcontent txtpos)
					(setq textlist (append textlist (list (list txtcontent txtpos))))
				)
			)
		)
		; Neu khong co thu tu selection, su dung thu tu mac dinh
		(progn
			(setq n 0)
			(repeat (sslength ss)
				(setq ent (ssname ss n))
				(setq txtobj (vlax-ename->vla-object ent))
				(setq objname (vla-get-objectname txtobj))

				; Xu ly theo loai doi tuong
				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
						; Trich xuat so va nhan he so
						(setq numvalue (EXTRACT-NUMBER txtcontent))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq txtcontent (rtos (car numvalue) 2 decimal-places))
									; Khong co don vi - nhan he so
									(setq txtcontent (rtos (* numvalue factor) 2 decimal-places))
								)
							)
							; Neu khong trich xuat duoc so, giu nguyen text
							(setq txtcontent txtcontent)
						)
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
						; Trich xuat so va nhan he so
						(setq numvalue (EXTRACT-NUMBER txtcontent))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq txtcontent (rtos (car numvalue) 2 decimal-places))
									; Khong co don vi - nhan he so
									(setq txtcontent (rtos (* numvalue factor) 2 decimal-places))
								)
							)
							; Neu khong trich xuat duoc so, giu nguyen text
							(setq txtcontent txtcontent)
						)
					)
					; DIMENSION (tat ca cac loai)
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						; Lay gia tri Measurement, nhan he so va lam tron
						(setq measurement (vla-get-measurement txtobj))
						(setq txtcontent (rtos (* measurement factor) 2 decimal-places))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; Loai khac - bo qua
					(T
						(setq txtcontent nil txtpos nil)
					)
				)

				; Them vao list neu co noi dung
				(if (and txtcontent txtpos)
					(setq textlist (append textlist (list (list txtcontent txtpos))))
				)
				(setq n (+ n 1))
			)
		)
	)
	textlist
)

; ===================================================================
; HAM PHU TRO CHO ET
; ===================================================================

; Ham thuc hien Table mode
(defun ET-EXECUTE-TABLE ( ss xlcells startrow startcol / textlist newrow newcol dwgpath dwgname handlelist commenttext targetcell)
	; Chuyen doi selection set thanh text list - dung ham E5 de co toa do
	(if (= *E6-number* "Y")
		(setq textlist (E5-CONVERT-SS-TO-TEXTLIST-NUMBER ss))
		(setq textlist (E5-CONVERT-SS-TO-TEXTLIST ss))
	)

	; Xuat text theo hang ngang
	(setq newcol startcol)
	(foreach txt textlist
		(SETCELLTEXT xlcells startrow newcol (car txt))
		(setq newcol (+ newcol 1))
	)

	; Ghi handle neu can
	(if (= *E6-handle* "Y")
		(progn
			(setq dwgpath (vla-get-fullname (vla-get-activedocument (vlax-get-acad-object))))
			(setq dwgname (vl-filename-base dwgpath))
			(setq handlelist (GET-HANDLES ss))
			(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
			(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
			(WRITE-COMMENT-TO-CELL targetcell commenttext)
		)
	)

	; Nhay chuot
	(setq newrow (+ startrow (atoi *E6-jump*)) newcol newcol)
	(MOVE-CURSOR xlcells newrow newcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa newcol)))
)

; Ham thuc hien Col mode
(defun ET-EXECUTE-COL ( ss xlcells startrow startcol / textlist sorted-textlist newrow newcol dwgpath dwgname handlelist commenttext targetcell)
	; Chuyen doi va sap xep
	(if (= *E6-number* "Y")
		(setq textlist (E5-CONVERT-SS-TO-TEXTLIST-NUMBER ss))
		(setq textlist (E5-CONVERT-SS-TO-TEXTLIST ss))
	)
	(setq sorted-textlist (ET-SORT-FOR-COL textlist))

	; Xuat theo cot
	(setq newrow startrow)
	(foreach txt sorted-textlist
		(SETCELLTEXT xlcells newrow startcol (car txt))
		(setq newrow (+ newrow 1))
	)

	; Ghi handle neu can
	(if (= *E6-handle* "Y")
		(progn
			(setq dwgpath (vla-get-fullname (vla-get-activedocument (vlax-get-acad-object))))
			(setq dwgname (vl-filename-base dwgpath))
			(setq handlelist (GET-HANDLES ss))
			(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
			(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
			(WRITE-COMMENT-TO-CELL targetcell commenttext)
		)
	)

	; Nhay chuot
	(setq newrow (+ newrow (atoi *E6-jump*)) newcol startcol)
	(MOVE-CURSOR xlcells newrow newcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa newcol)))
)

; Ham thuc hien Row mode
(defun ET-EXECUTE-ROW ( ss xlcells startrow startcol / textlist sorted-textlist newcol newrow dwgpath dwgname handlelist commenttext targetcell)
	(if (= *E6-number* "Y")
		(setq textlist (E5-CONVERT-SS-TO-TEXTLIST-NUMBER ss))
		(setq textlist (E5-CONVERT-SS-TO-TEXTLIST ss))
	)
	(setq sorted-textlist (ET-SORT-FOR-ROW textlist))

	; Xuat theo hang
	(setq newcol startcol)
	(foreach txt sorted-textlist
		(SETCELLTEXT xlcells startrow newcol (car txt))
		(setq newcol (+ newcol 1))
	)

	; Ghi handle neu can
	(if (= *E6-handle* "Y")
		(progn
			(setq dwgpath (vla-get-fullname (vla-get-activedocument (vlax-get-acad-object))))
			(setq dwgname (vl-filename-base dwgpath))
			(setq handlelist (GET-HANDLES ss))
			(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
			(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
			(WRITE-COMMENT-TO-CELL targetcell commenttext)
		)
	)

	; Nhay chuot
	(setq newrow (+ startrow (atoi *E6-jump*)) newcol newcol)
	(MOVE-CURSOR xlcells newrow newcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa newcol)))
)

; Ham thuc hien Cell mode
(defun ET-EXECUTE-CELL ( ss xlcells startrow startcol / textlist result-text newrow)
	(setq textlist (CONVERT-SS-TO-TEXTLIST ss))
	(if (= *E6-symbol* "+")
		(setq result-text (ET-CREATE-FORMULA textlist))
		(setq result-text (ET-JOIN-TEXT textlist))
	)
	(SETCELLTEXT xlcells startrow startcol result-text)
	(setq newrow (+ startrow 1 (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
)

; Ham thuc hien Array mode
(defun ET-EXECUTE-ARRAY ( ss xlcells startrow startcol / textlist newrow)
	(setq textlist (CONVERT-SS-TO-TEXTLIST ss))
	(E5-EXPORT-ARRAY textlist xlcells startrow startcol)
	(setq newrow (+ startrow (E5-GET-ARRAY-ROWS textlist) (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
)

; Ham thuc hien Handle mode
(defun ET-EXECUTE-HANDLE ( ss xlcells startrow startcol / handlelist commenttext dwgpath dwgname newrow targetcell)
	(setq dwgpath (vla-get-fullname ActDoc)
		  dwgname (vl-filename-base dwgpath))
	(setq handlelist (GET-HANDLES ss))
	(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
	(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
	(WRITE-COMMENT-TO-CELL targetcell commenttext)
	(setq newrow (+ startrow (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Da ghi " (itoa (length handlelist)) " handle"))
)

; Ham thuc hien Value mode
(defun ET-EXECUTE-VALUE ( ss xlcells startrow startcol / celltext handlelist commenttext dwgpath dwgname newrow targetcell)
	(setq dwgpath (vla-get-fullname ActDoc)
		  dwgname (vl-filename-base dwgpath))
	(setq celltext (ET-CREATE-CELL-TEXT ss))
	(setq handlelist (GET-HANDLES ss))
	(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))

	(SETCELLTEXT xlcells startrow startcol celltext)
	(if (= *E6-handle* "Y")
		(progn
			(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
			(WRITE-COMMENT-TO-CELL targetcell commenttext)
		)
	)
	(setq newrow (+ startrow (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
)

; Ham tao cong thuc cho ET
(defun ET-CREATE-FORMULA ( textlist / result)
	(setq result (strcat "=" (car (car textlist))))
	(foreach txt (cdr textlist)
		(setq result (strcat result "+" (car txt)))
	)
	result
)

; Ham noi text cho ET
(defun ET-JOIN-TEXT ( textlist / result)
	(setq result (car (car textlist)))
	(foreach txt (cdr textlist)
		(setq result (strcat result *E6-symbol* (car txt)))
	)
	result
)

; Ham ho tro - Sap xep cho Col mode
(defun ET-SORT-FOR-COL ( textlist / )
	; Sap xep theo Y giam dan (top to bottom) - dung Z coordinate
	(vl-sort textlist '(lambda (a b) (> (caddr (cadr a)) (caddr (cadr b)))))
)

; Ham ho tro - Sap xep cho Row mode
(defun ET-SORT-FOR-ROW ( textlist / )
	; Sap xep theo X tang dan (left to right) - dung X coordinate
	(vl-sort textlist '(lambda (a b) (< (car (cadr a)) (car (cadr b)))))
)

; Ham tao noi dung cell cho ET
(defun ET-CREATE-CELL-TEXT ( ss / textlist result)
	(setq textlist (CONVERT-SS-TO-TEXTLIST ss))
	(if (= *E6-symbol* "+")
		(setq result (ET-CREATE-FORMULA textlist))
		(setq result (ET-JOIN-TEXT textlist))
	)
	result
)

; ===================================================================
; TE - XUAT TABLE CAD SANG EXCEL
; ===================================================================

(defun C:TE ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	table-ent
	table-obj
	rows
	cols
	row
	col
	cell-text
	newrow
	handlelist
	commenttext
	dwgpath
	dwgname
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon table CAD
	(princ "\nChon table CAD de xuat sang Excel: ")
	(setq table-ent (car (entsel)))

	(if (and table-ent
			 (setq table-obj (vlax-ename->vla-object table-ent))
			 (= (vlax-get table-obj 'ObjectName) "AcDbTable"))
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Lay kich thuoc table
			(setq rows (vla-get-Rows table-obj)
				  cols (vla-get-Columns table-obj))

			(princ (strcat "\nTable co " (itoa rows) " hang va " (itoa cols) " cot"))

			; Xuat du lieu table sang Excel
			(setq row 0)
			(while (< row rows)
				(setq col 0)
				(while (< col cols)
					; Lay noi dung cell
					(setq cell-text (vl-catch-all-apply '(lambda ()
						(vla-GetText table-obj row col)
					)))

					; Neu lay duoc text, xu ly MTEXT format va ghi vao Excel
					(if (and (not (vl-catch-all-error-p cell-text))
							 (> (strlen cell-text) 0))
						(progn
							; Xu ly MTEXT format neu can - dung ham chuyen doi dac biet cho table
							(setq cell-text (CLEAN-TABLE-TEXT cell-text))
							; Ghi vao Excel
							(SETCELLTEXT xlcells (+ startrow row) (+ startcol col) cell-text)
						)
					)
					(setq col (+ col 1))
				)
				(setq row (+ row 1))
			)

			; Ghi handle neu can
			(if (= *E6-handle* "Y")
				(progn
					(setq handlelist (list (vla-get-handle table-obj)))
					(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
					; Ghi comment vao cell dau tien
					(setq result (vl-catch-all-apply '(lambda ()
						(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
						(WRITE-COMMENT-TO-CELL targetcell commenttext)
					)))
					(if (vl-catch-all-error-p result)
						(progn
							; Thu cach khac
							(setq result (vl-catch-all-apply '(lambda ()
								(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
								(setq worksheet (vlax-get-property xlcells "Parent"))
								(setq targetcell (vlax-get-property worksheet "Range" celladdress))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
						)
					)
				)
			)

			; Dong khung neu can
			(if (= *E6-frame* "Y")
				(TE-ADD-BORDERS xlcells startrow startcol (+ startrow rows -1) (+ startcol cols -1))
			)

			; Nhay chuot xuong duoi table
			(setq newrow (+ startrow rows (atoi *E6-jump*)))
			(MOVE-CURSOR xlcells newrow startcol)
			(princ (strcat "\nHoan thanh! Da xuat table " (itoa rows) "x" (itoa cols) " sang Excel"))
			(princ (strcat "\nChuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
		)
		(princ "\nKhong phai table CAD hoac khong chon duoc!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; Ham dong khung cho TE
(defun TE-ADD-BORDERS ( xlcells startrow startcol endrow endcol / range worksheet xlapp startaddr endaddr result)
	; Thu cach 1: Su dung Range truc tiep
	(setq result (vl-catch-all-apply '(lambda ()
		(setq startaddr (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
		(setq endaddr (strcat (COLUMN-NUMBER-TO-LETTER endcol) (itoa endrow)))
		(setq worksheet (vlax-get-property xlcells "Parent"))
		(setq range (vlax-get-property worksheet "Range" (strcat startaddr ":" endaddr)))
		(vlax-put-property (vlax-get-property range "Borders") "LineStyle" 1)
	)))

	; Neu that bai, thu cach 2: Su dung Excel Application
	(if (vl-catch-all-error-p result)
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				(setq xlapp (vlax-get-object "Excel.Application"))
				(setq worksheet (vlax-get-property xlapp "ActiveSheet"))
				(setq startaddr (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
				(setq endaddr (strcat (COLUMN-NUMBER-TO-LETTER endcol) (itoa endrow)))
				(setq range (vlax-get-property worksheet "Range" (strcat startaddr ":" endaddr)))
				; Dong tat ca cac vien
				(vlax-put-property (vlax-get-property range "Borders" 7) "LineStyle" 1)  ; xlEdgeLeft
				(vlax-put-property (vlax-get-property range "Borders" 8) "LineStyle" 1)  ; xlEdgeTop
				(vlax-put-property (vlax-get-property range "Borders" 9) "LineStyle" 1)  ; xlEdgeBottom
				(vlax-put-property (vlax-get-property range "Borders" 10) "LineStyle" 1) ; xlEdgeRight
				(vlax-put-property (vlax-get-property range "Borders" 11) "LineStyle" 1) ; xlInsideVertical
				(vlax-put-property (vlax-get-property range "Borders" 12) "LineStyle" 1) ; xlInsideHorizontal
			)))
		)
	)
)

; ===================================================================
; KET THUC FILE
; ===================================================================

; Thiet lap chu ky
(setvar "MODEMACRO" "**Zit Đại Ka**")

(princ "\n╔══════════════════════════════════════════════════════════════╗")
(princ "\n║              CAD TO EXCEL - PHIEN BAN 2.0 TOI UU            ║")
(princ "\n║                     Tac gia: Zit Đại Ka                     ║")
(princ "\n║                   Ngay: 19/12/2024                          ║")
(princ "\n╠══════════════════════════════════════════════════════════════╣")
(princ "\n║ DANH SACH LENH:                                              ║")
(princ "\n║  E1  - Xuat text/dim vao cell (co he so factor)             ║")
(princ "\n║  E2  - Xuat text/dim vao cell (text giu nguyen)             ║")
(princ "\n║  E3  - Xuat theo 3 che do (Col/Row/Array)                   ║")
(princ "\n║  E4  - Chi ghi handle vao comment                           ║")
(princ "\n║  E5  - Xuat table vao Excel                                 ║")
(princ "\n║  E6  - Thiet lap toan cuc                                   ║")
(princ "\n║  E7  - Cong tac Handle (Toggle On/Off)                      ║")
(princ "\n║  E8  - Cong tac Factor (Toggle 1/0.001)                     ║")
(princ "\n║  E9  - Cong tac Number (Toggle Y/N)                         ║")
(princ "\n║  E11 - Xuat text/dim voi he so nhan them                    ║")
(princ "\n║  ET  - Lenh tong hop toi uu                                 ║")
(princ "\n║  TE  - Xuat table CAD sang Excel                           ║")
(princ "\n╠══════════════════════════════════════════════════════════════╣")
(princ "\n║ TINH NANG MOI:                                               ║")
(princ "\n║  - Ho tro thu tu selection (click theo thu tu)              ║")
(princ "\n║  - Chuyen doi ky hieu dac biet (%%c->Ø, %%p->±, %%d->°)      ║")
(princ "\n║  - Xu ly MTEXT format codes                                  ║")
(princ "\n║  - Tao cong thuc Excel khi symbol='+'                       ║")
(princ "\n║  - He so nhan them cho E11                                   ║")
(princ "\n╚══════════════════════════════════════════════════════════════╝")
(princ "\n=== LOADED SUCCESSFULLY ===")
(princ)



;;----------------------------------------------

;; ========================================================================
;; ZoomToHandle.lsp - Zoom den doi tuong tu handle trong Excel
;; Phien ban: 1.0
;; Ngay phat hanh: 2024
;; Tac gia: **Zit Dai Ka**
;; ========================================================================
;; 
;; CHUC NANG:
;; - Lay ma handle tu Excel cell hoac comment
;; - Tim va zoom den doi tuong trong AutoCAD
;; - Highlight doi tuong duoc tim thay
;; - Ho tro nhieu handle cach nhau boi dau ";"
;;
;; LENH:
;; ZTH - Zoom To Handle tu Excel
;; STH - Select To Handle tu Excel (zoom va select)
;; HTC - Handle To CAD (mo file CAD tu duong dan trong Excel)
;;
;; ========================================================================

(setvar "MODEMACRO" "**Zit Dai Ka**")

;; Ham chinh - Zoom den handle tu Excel
(defun C:ZTH (/ excel-app excel-wb excel-ws active-cell comment-obj comment-text cell-value
              handle-text handle-list clean-handle obj found-count zoom-objects
              min-pt max-pt i file-pos)



  ;; Ket noi den Excel
  (setq excel-app (vlax-get-or-create-object "Excel.Application"))

  (if excel-app
    (progn


      ;; Lay workbook va worksheet hien tai
      (setq excel-wb nil)
      (setq excel-ws nil)
      (setq active-cell nil)

      (vl-catch-all-apply
        '(lambda ()
           (setq excel-wb (vlax-get-property excel-app "ActiveWorkbook"))
           (setq excel-ws (vlax-get-property excel-app "ActiveSheet"))
           (setq active-cell (vlax-get-property excel-app "ActiveCell"))
         )
      )

      (if (not active-cell)
        (progn

          (vlax-release-object excel-app)
        )
        (progn


          ;; Lay text tu comment neu co
          (setq comment-text "")
          (setq comment-obj nil)

          ;; Thu lay comment object
          (setq comment-result
            (vl-catch-all-apply
              '(lambda ()
                 (setq comment-obj (vlax-get-property active-cell "Comment"))
               )
            )
          )

          ;; Neu co comment, lay text
          (if (vl-catch-all-error-p comment-result)
            (progn
              (princ "\nKhong co comment hoac loi khi lay comment.")
              (setq comment-text "")
            )
            (progn
              (if comment-obj
                (progn
                  (princ "\nDa tim thay comment object.")
                  ;; Thu lay comment text bang nhieu cach
                  (setq comment-text nil)

                  ;; Cach 1: Dung vlax-get-property
                  (setq text-result1
                    (vl-catch-all-apply
                      '(lambda ()
                         (setq comment-text (vlax-get-property comment-obj "Text"))
                       )
                    )
                  )

                  ;; Cach 2: Neu cach 1 that bai, thu dung vlax-invoke
                  (if (or (vl-catch-all-error-p text-result1) (not comment-text))
                    (setq text-result2
                      (vl-catch-all-apply
                        '(lambda ()
                           (setq comment-text (vlax-invoke-method comment-obj "Text"))
                         )
                      )
                    )
                  )

                  ;; Cach 3: Thu truy cap truc tiep
                  (if (or (not comment-text) (= comment-text ""))
                    (setq text-result3
                      (vl-catch-all-apply
                        '(lambda ()
                           (setq comment-text (vlax-get comment-obj "Text"))
                         )
                      )
                    )
                  )

                  ;; Kiem tra ket qua
                  (if (and comment-text (/= comment-text ""))
                    (progn
                      ;; Xu ly variant value
                      (if (= (type comment-text) 'VARIANT)
                        (setq comment-text (vlax-variant-value comment-text))
                      )
                      (if (not (= (type comment-text) 'STR))
                        (setq comment-text (vl-princ-to-string comment-text))
                      )
                      (princ (strcat "\nComment text: " comment-text))
                    )
                    (progn
                      (princ "\nKhong lay duoc comment text bang bat ky cach nao.")
                      (setq comment-text "")
                    )
                  )
                )
                (progn
                  (princ "\nComment object la nil.")
                  (setq comment-text "")
                )
              )
            )
          )

          ;; Xu ly comment text - loai bo phan FileCad neu co
          (if (and comment-text (/= comment-text ""))
            (progn
              ;; Tim vi tri cua FileCad hoac \n de cat chuoi
              (setq file-pos (vl-string-search "FILECAD:" (strcase comment-text)))
              (if (not file-pos)
                (setq file-pos (vl-string-search "\n" comment-text))
              )

              (if file-pos
                (setq handle-text (vl-string-trim " \t\n\r" (substr comment-text 1 file-pos)))
                (setq handle-text (vl-string-trim " \t\n\r" comment-text))
              )

              ;; Neu van chua co handle, thu tim trong toan bo comment
              (if (or (not handle-text) (= handle-text ""))
                (setq handle-text (ZTH-extract-handles-from-text comment-text))
              )
            )
            (setq handle-text "")
          )

          ;; Neu comment khong co handle, lay tu cell value
          (if (or (not handle-text) (= handle-text ""))
            (progn
              (setq cell-value nil)
              (setq value-result
                (vl-catch-all-apply
                  '(lambda ()
                     (setq cell-value (vlax-get-property active-cell "Value"))
                   )
                )
              )

              (if (and (not (vl-catch-all-error-p value-result)) cell-value)
                (progn
                  ;; Xu ly variant value
                  (if (= (type cell-value) 'VARIANT)
                    (setq cell-value (vlax-variant-value cell-value))
                  )
                  (setq cell-value (vl-princ-to-string cell-value))
                  (princ (strcat "\nCell value: " cell-value))
                  (setq file-pos (vl-string-search "FILECAD:" (strcase cell-value)))
                  (if file-pos
                    (setq handle-text (vl-string-trim " \t\n\r" (substr cell-value 1 file-pos)))
                    (setq handle-text (vl-string-trim " \t\n\r" cell-value))
                  )
                )
                (setq handle-text "")
              )
            )
          )

          ;; Kiem tra co handle hay khong
          (if (or (not handle-text) (= handle-text ""))
            (progn
              (princ "\nKhong tim thay ma handle trong o hoac comment.")
              (vlax-release-object excel-app)
            )
            (progn
              (princ (strcat "\nHandle text: " handle-text))

              ;; Tach chuoi handle theo dau ";"
              (setq handle-list (ZTH-split-string handle-text ";"))
              (setq found-count 0)
              (setq zoom-objects '())

              ;; Kiem tra tung handle
              (foreach handle handle-list
                (setq clean-handle (ZTH-clean-handle handle))
                (if (and clean-handle (/= clean-handle ""))
                  (progn
                    (setq obj (ZTH-get-object-by-handle clean-handle))
                    (if obj
                      (progn
                        (setq found-count (1+ found-count))
                        (setq zoom-objects (cons obj zoom-objects))
                        (princ (strcat "\nTim thay doi tuong: " clean-handle))
                      )
                      (princ (strcat "\nKhong tim thay doi tuong: " clean-handle))
                    )
                  )
                )
              )

              ;; Neu tim thay doi tuong
              (if (> found-count 0)
                (progn
                  ;; Zoom den tat ca doi tuong truoc
                  (if (> (length zoom-objects) 1)
                    (progn
                      ;; Tinh bounding box chung cho tat ca doi tuong
                      (setq all-min-pt nil)
                      (setq all-max-pt nil)

                      (foreach obj zoom-objects
                        (vl-catch-all-apply
                          '(lambda ()
                             (vla-getboundingbox obj 'temp-min 'temp-max)
                             (if (and temp-min temp-max)
                               (progn
                                 (setq temp-min (vlax-safearray->list temp-min))
                                 (setq temp-max (vlax-safearray->list temp-max))

                                 (if (not all-min-pt)
                                   (setq all-min-pt temp-min)
                                   (setq all-min-pt (list (min (car all-min-pt) (car temp-min))
                                                          (min (cadr all-min-pt) (cadr temp-min))
                                                          (min (caddr all-min-pt) (caddr temp-min))))
                                 )

                                 (if (not all-max-pt)
                                   (setq all-max-pt temp-max)
                                   (setq all-max-pt (list (max (car all-max-pt) (car temp-max))
                                                          (max (cadr all-max-pt) (cadr temp-max))
                                                          (max (caddr all-max-pt) (caddr temp-max))))
                                 )
                               )
                             )
                           )
                        )
                      )

                      ;; Zoom den vung chung
                      (if (and all-min-pt all-max-pt)
                        (progn
                          (command "_.zoom" "_window" all-min-pt all-max-pt)
                          (command "_.zoom" "0.8x")
                        )
                      )
                    )
                    ;; Neu chi co 1 doi tuong
                    (progn
                      (setq obj (car zoom-objects))
                      (vl-catch-all-apply
                        '(lambda ()
                           (vla-getboundingbox obj 'min-pt 'max-pt)
                           (if (and min-pt max-pt)
                             (progn
                               (setq min-pt (vlax-safearray->list min-pt))
                               (setq max-pt (vlax-safearray->list max-pt))
                               (command "_.zoom" "_window" min-pt max-pt)
                               (command "_.zoom" "0.8x")
                             )
                           )
                         )
                      )
                    )
                  )

                  ;; Highlight cac doi tuong sau khi zoom
                  (foreach obj zoom-objects
                    (setq ent (vlax-vla-object->ename obj))
                    (if ent
                      (progn
                        (redraw ent 3) ; Highlight
                        (princ (strcat "\nDa highlight doi tuong: " (vla-get-handle obj)))
                      )
                    )
                  )

                  (princ (strcat "\nHoan thanh! Da zoom va highlight " (itoa found-count) " doi tuong."))
                )
                (princ "\nKhong tim thay doi tuong nao voi cac handle da cung cap.")
              )

              (vlax-release-object excel-app)
            )
          )
        )
      )
    )
    (princ "\nLoi: Khong the ket noi den Excel.")
  )
  
  (princ)
)

;; Ham tach chuoi theo delimiter
(defun ZTH-split-string (str delimiter / pos result temp)
  (setq result '())
  (setq temp str)

  (while (setq pos (vl-string-search delimiter temp))
    (if (> pos 0)
      (setq result (cons (substr temp 1 pos) result))
    )
    (setq temp (substr temp (+ pos (strlen delimiter) 1)))
  )

  ;; Them phan cuoi cung
  (if (and temp (/= temp ""))
    (setq result (cons temp result))
  )

  (reverse result)
)

;; Ham lam sach handle (loai bo khoang trang va dau ')
(defun ZTH-clean-handle (handle-str / clean-str)
  (setq clean-str (vl-string-trim " \t\n\r" handle-str))

  ;; Loai bo dau ' o dau neu co
  (if (and (> (strlen clean-str) 0) (= (substr clean-str 1 1) "'"))
    (setq clean-str (substr clean-str 2))
  )

  ;; Loai bo cac ky tu khong phai hex (chi giu lai 0-9, A-F)
  (setq clean-str (ZTH-extract-hex-only clean-str))

  (vl-string-trim " \t\n\r" clean-str)
)

;; Ham chi lay cac ky tu hex hop le tu chuoi
(defun ZTH-extract-hex-only (str / result i char)
  (setq result "")
  (setq i 1)

  (while (<= i (strlen str))
    (setq char (substr str i 1))
    (if (or (and (>= char "0") (<= char "9"))
            (and (>= (strcase char) "A") (<= (strcase char) "F")))
      (setq result (strcat result (strcase char)))
    )
    (setq i (1+ i))
  )

  result
)

;; Ham tim tat ca handle trong text (tim cac chuoi hex 6-8 ky tu)
(defun ZTH-extract-handles-from-text (text / handles i j temp-str char result)
  (setq handles '())
  (setq i 1)
  (setq text (strcase text))

  (while (<= i (strlen text))
    (setq char (substr text i 1))

    ;; Neu gap ky tu hex, bat dau thu thap
    (if (or (and (>= char "0") (<= char "9"))
            (and (>= char "A") (<= char "F")))
      (progn
        (setq temp-str "")
        (setq j i)

        ;; Thu thap cac ky tu hex lien tiep
        (while (and (<= j (strlen text))
                    (progn
                      (setq char (substr text j 1))
                      (or (and (>= char "0") (<= char "9"))
                          (and (>= char "A") (<= char "F")))
                    ))
          (setq temp-str (strcat temp-str char))
          (setq j (1+ j))
        )

        ;; Neu chuoi hex co do dai 6-8 ky tu, coi nhu la handle
        (if (and (>= (strlen temp-str) 6) (<= (strlen temp-str) 8))
          (setq handles (cons temp-str handles))
        )

        (setq i j)
      )
      (setq i (1+ i))
    )
  )

  ;; Tra ve chuoi handle cach nhau boi dau ;
  (if handles
    (apply 'strcat (reverse (cons (car handles)
                                  (mapcar '(lambda (x) (strcat ";" x)) (cdr handles)))))
    ""
  )
)

;; Ham lay doi tuong theo handle
(defun ZTH-get-object-by-handle (handle-str / obj)
  (setq obj nil)
  
  (if (and handle-str (/= handle-str ""))
    (progn
      (vl-catch-all-apply
        '(lambda ()
           (setq obj (vlax-ename->vla-object (handent handle-str)))
         )
      )
    )
  )
  
  obj
)

;; Ham chinh - Select To Handle tu Excel
(defun C:STH (/ excel-app excel-wb excel-ws active-cell comment-obj comment-text cell-value
              handle-text handle-list clean-handle obj found-count zoom-objects select-objects
              min-pt max-pt i file-pos all-min-pt all-max-pt temp-min temp-max ent ss)

  (princ "\nBat dau ket noi Excel...")

  ;; Ket noi den Excel
  (setq excel-app (vlax-get-or-create-object "Excel.Application"))

  (if excel-app
    (progn
      (princ "\nDa ket noi Excel thanh cong.")

      ;; Lay workbook va worksheet hien tai
      (setq excel-wb nil)
      (setq excel-ws nil)
      (setq active-cell nil)

      (vl-catch-all-apply
        '(lambda ()
           (setq excel-wb (vlax-get-property excel-app "ActiveWorkbook"))
           (setq excel-ws (vlax-get-property excel-app "ActiveSheet"))
           (setq active-cell (vlax-get-property excel-app "ActiveCell"))
         )
      )

      (if (not active-cell)
        (progn
          (princ "\nLoi: Khong the lay active cell tu Excel.")
          (vlax-release-object excel-app)
        )
        (progn
          (princ "\nDa lay active cell thanh cong.")

          ;; Lay text tu comment neu co
          (setq comment-text "")
          (setq comment-obj nil)

          ;; Thu lay comment object
          (setq comment-result
            (vl-catch-all-apply
              '(lambda ()
                 (setq comment-obj (vlax-get-property active-cell "Comment"))
               )
            )
          )

          ;; Neu co comment, lay text
          (if (vl-catch-all-error-p comment-result)
            (progn
              (princ "\nKhong co comment hoac loi khi lay comment.")
              (setq comment-text "")
            )
            (progn
              (if comment-obj
                (progn
                  (princ "\nDa tim thay comment object.")
                  ;; Thu lay comment text bang nhieu cach
                  (setq comment-text nil)

                  ;; Cach 1: Dung vlax-get-property
                  (setq text-result1
                    (vl-catch-all-apply
                      '(lambda ()
                         (setq comment-text (vlax-get-property comment-obj "Text"))
                       )
                    )
                  )

                  ;; Cach 2: Neu cach 1 that bai, thu dung vlax-invoke
                  (if (or (vl-catch-all-error-p text-result1) (not comment-text))
                    (setq text-result2
                      (vl-catch-all-apply
                        '(lambda ()
                           (setq comment-text (vlax-invoke-method comment-obj "Text"))
                         )
                      )
                    )
                  )

                  ;; Cach 3: Thu truy cap truc tiep
                  (if (or (not comment-text) (= comment-text ""))
                    (setq text-result3
                      (vl-catch-all-apply
                        '(lambda ()
                           (setq comment-text (vlax-get comment-obj "Text"))
                         )
                      )
                    )
                  )

                  ;; Kiem tra ket qua
                  (if (and comment-text (/= comment-text ""))
                    (progn
                      ;; Xu ly variant value
                      (if (= (type comment-text) 'VARIANT)
                        (setq comment-text (vlax-variant-value comment-text))
                      )
                      (if (not (= (type comment-text) 'STR))
                        (setq comment-text (vl-princ-to-string comment-text))
                      )
                      (princ (strcat "\nComment text: " comment-text))
                    )
                    (progn
                      (princ "\nKhong lay duoc comment text bang bat ky cach nao.")
                      (setq comment-text "")
                    )
                  )
                )
                (progn
                  (princ "\nComment object la nil.")
                  (setq comment-text "")
                )
              )
            )
          )

          ;; Xu ly comment text - loai bo phan FileCad neu co
          (if (and comment-text (/= comment-text ""))
            (progn
              ;; Tim vi tri cua FileCad hoac \n de cat chuoi
              (setq file-pos (vl-string-search "FILECAD:" (strcase comment-text)))
              (if (not file-pos)
                (setq file-pos (vl-string-search "\n" comment-text))
              )

              (if file-pos
                (setq handle-text (vl-string-trim " \t\n\r" (substr comment-text 1 file-pos)))
                (setq handle-text (vl-string-trim " \t\n\r" comment-text))
              )

              ;; Neu van chua co handle, thu tim trong toan bo comment
              (if (or (not handle-text) (= handle-text ""))
                (setq handle-text (STH-extract-handles-from-text comment-text))
              )
            )
            (setq handle-text "")
          )

          ;; Neu comment khong co handle, lay tu cell value
          (if (or (not handle-text) (= handle-text ""))
            (progn
              (setq cell-value nil)
              (setq value-result
                (vl-catch-all-apply
                  '(lambda ()
                     (setq cell-value (vlax-get-property active-cell "Value"))
                   )
                )
              )

              (if (and (not (vl-catch-all-error-p value-result)) cell-value)
                (progn
                  ;; Xu ly variant value
                  (if (= (type cell-value) 'VARIANT)
                    (setq cell-value (vlax-variant-value cell-value))
                  )
                  (setq cell-value (vl-princ-to-string cell-value))
                  (princ (strcat "\nCell value: " cell-value))
                  (setq file-pos (vl-string-search "FILECAD:" (strcase cell-value)))
                  (if file-pos
                    (setq handle-text (vl-string-trim " \t\n\r" (substr cell-value 1 file-pos)))
                    (setq handle-text (vl-string-trim " \t\n\r" cell-value))
                  )
                )
                (setq handle-text "")
              )
            )
          )

          ;; Kiem tra co handle hay khong
          (if (or (not handle-text) (= handle-text ""))
            (progn
              (princ "\nKhong tim thay ma handle trong o hoac comment.")
              (vlax-release-object excel-app)
            )
            (progn
              (princ (strcat "\nHandle text: " handle-text))

              ;; Tach chuoi handle theo dau ";"
              (setq handle-list (STH-split-string handle-text ";"))
              (setq found-count 0)
              (setq zoom-objects '())
              (setq select-objects '())

              ;; Kiem tra tung handle
              (foreach handle handle-list
                (setq clean-handle (STH-clean-handle handle))
                (if (and clean-handle (/= clean-handle ""))
                  (progn
                    (setq obj (STH-get-object-by-handle clean-handle))
                    (if obj
                      (progn
                        (setq found-count (1+ found-count))
                        (setq zoom-objects (cons obj zoom-objects))
                        (setq ent (vlax-vla-object->ename obj))
                        (if ent
                          (setq select-objects (cons ent select-objects))
                        )
                        (princ (strcat "\nTim thay doi tuong: " clean-handle))
                      )
                      (princ (strcat "\nKhong tim thay doi tuong: " clean-handle))
                    )
                  )
                )
              )

              ;; Neu tim thay doi tuong
              (if (> found-count 0)
                (progn
                  ;; Zoom den tat ca doi tuong truoc
                  (if (> (length zoom-objects) 1)
                    (progn
                      ;; Tinh bounding box chung cho tat ca doi tuong
                      (setq all-min-pt nil)
                      (setq all-max-pt nil)

                      (foreach obj zoom-objects
                        (vl-catch-all-apply
                          '(lambda ()
                             (vla-getboundingbox obj 'temp-min 'temp-max)
                             (if (and temp-min temp-max)
                               (progn
                                 (setq temp-min (vlax-safearray->list temp-min))
                                 (setq temp-max (vlax-safearray->list temp-max))

                                 (if (not all-min-pt)
                                   (setq all-min-pt temp-min)
                                   (setq all-min-pt (list (min (car all-min-pt) (car temp-min))
                                                          (min (cadr all-min-pt) (cadr temp-min))
                                                          (min (caddr all-min-pt) (caddr temp-min))))
                                 )

                                 (if (not all-max-pt)
                                   (setq all-max-pt temp-max)
                                   (setq all-max-pt (list (max (car all-max-pt) (car temp-max))
                                                          (max (cadr all-max-pt) (cadr temp-max))
                                                          (max (caddr all-max-pt) (caddr temp-max))))
                                 )
                               )
                             )
                           )
                        )
                      )

                      ;; Zoom den vung chung
                      (if (and all-min-pt all-max-pt)
                        (progn
                          (command "_.zoom" "_window" all-min-pt all-max-pt)
                          (command "_.zoom" "0.8x")
                        )
                      )
                    )
                    ;; Neu chi co 1 doi tuong
                    (progn
                      (setq obj (car zoom-objects))
                      (vl-catch-all-apply
                        '(lambda ()
                           (vla-getboundingbox obj 'min-pt 'max-pt)
                           (if (and min-pt max-pt)
                             (progn
                               (setq min-pt (vlax-safearray->list min-pt))
                               (setq max-pt (vlax-safearray->list max-pt))
                               (command "_.zoom" "_window" min-pt max-pt)
                               (command "_.zoom" "0.8x")
                             )
                           )
                         )
                      )
                    )
                  )

                  ;; Select cac doi tuong sau khi zoom
                  (if select-objects
                    (progn
                      ;; Tao selection set moi
                      (setq ss (ssadd))
                      (foreach ent select-objects
                        (if ent
                          (setq ss (ssadd ent ss))
                        )
                      )

                      ;; Set selection set hien tai
                      (if (> (sslength ss) 0)
                        (progn
                          (sssetfirst nil ss)
                          (princ (strcat "\nDa select " (itoa (sslength ss)) " doi tuong."))
                        )
                        (princ "\nKhong co doi tuong nao de select.")
                      )
                    )
                  )

                  (princ (strcat "\nHoan thanh! Da zoom va select " (itoa found-count) " doi tuong."))
                )
                (princ "\nKhong tim thay doi tuong nao voi cac handle da cung cap.")
              )

              (vlax-release-object excel-app)
            )
          )
        )
      )
    )
    (princ "\nLoi: Khong the ket noi den Excel.")
  )

  (princ)
)

;; Ham ho tro cho STH - tuong tu ZTH nhung co ten khac
(defun STH-split-string (str delimiter / pos result temp)
  (ZTH-split-string str delimiter)
)

(defun STH-clean-handle (handle-str)
  (ZTH-clean-handle handle-str)
)

(defun STH-extract-handles-from-text (text)
  (ZTH-extract-handles-from-text text)
)

(defun STH-get-object-by-handle (handle-str)
  (ZTH-get-object-by-handle handle-str)
)

;; Ham chinh - Handle To CAD (mo file CAD)
(defun C:HTC (/ excel-app excel-wb excel-ws active-cell comment-obj comment-text cell-value
              file-path file-pos clean-path open-result open-result2 open-result3
              acad-app acad-docs new-doc short-path)

  (princ "\nBat dau ket noi Excel...")

  ;; Ket noi den Excel
  (setq excel-app (vlax-get-or-create-object "Excel.Application"))

  (if excel-app
    (progn
      (princ "\nDa ket noi Excel thanh cong.")

      ;; Lay workbook va worksheet hien tai
      (setq excel-wb nil)
      (setq excel-ws nil)
      (setq active-cell nil)

      (vl-catch-all-apply
        '(lambda ()
           (setq excel-wb (vlax-get-property excel-app "ActiveWorkbook"))
           (setq excel-ws (vlax-get-property excel-app "ActiveSheet"))
           (setq active-cell (vlax-get-property excel-app "ActiveCell"))
         )
      )

      (if (not active-cell)
        (progn
          (princ "\nLoi: Khong the lay active cell tu Excel.")
          (vlax-release-object excel-app)
        )
        (progn
          (princ "\nDa lay active cell thanh cong.")

          ;; Lay text tu comment neu co
          (setq comment-text "")
          (setq comment-obj nil)

          ;; Thu lay comment object
          (setq comment-result
            (vl-catch-all-apply
              '(lambda ()
                 (setq comment-obj (vlax-get-property active-cell "Comment"))
               )
            )
          )

          ;; Neu co comment, lay text
          (if (vl-catch-all-error-p comment-result)
            (progn
              (princ "\nKhong co comment hoac loi khi lay comment.")
              (setq comment-text "")
            )
            (progn
              (if comment-obj
                (progn
                  (princ "\nDa tim thay comment object.")
                  ;; Thu lay comment text bang nhieu cach
                  (setq comment-text nil)

                  ;; Cach 1: Dung vlax-get-property
                  (setq text-result1
                    (vl-catch-all-apply
                      '(lambda ()
                         (setq comment-text (vlax-get-property comment-obj "Text"))
                       )
                    )
                  )

                  ;; Cach 2: Neu cach 1 that bai, thu dung vlax-invoke
                  (if (or (vl-catch-all-error-p text-result1) (not comment-text))
                    (setq text-result2
                      (vl-catch-all-apply
                        '(lambda ()
                           (setq comment-text (vlax-invoke-method comment-obj "Text"))
                         )
                      )
                    )
                  )

                  ;; Cach 3: Thu truy cap truc tiep
                  (if (or (not comment-text) (= comment-text ""))
                    (setq text-result3
                      (vl-catch-all-apply
                        '(lambda ()
                           (setq comment-text (vlax-get comment-obj "Text"))
                         )
                      )
                    )
                  )

                  ;; Kiem tra ket qua
                  (if (and comment-text (/= comment-text ""))
                    (progn
                      ;; Xu ly variant value
                      (if (= (type comment-text) 'VARIANT)
                        (setq comment-text (vlax-variant-value comment-text))
                      )
                      (if (not (= (type comment-text) 'STR))
                        (setq comment-text (vl-princ-to-string comment-text))
                      )
                      (princ (strcat "\nComment text: " comment-text))
                    )
                    (progn
                      (princ "\nKhong lay duoc comment text bang bat ky cach nao.")
                      (setq comment-text "")
                    )
                  )
                )
                (progn
                  (princ "\nComment object la nil.")
                  (setq comment-text "")
                )
              )
            )
          )

          ;; Neu comment khong co duong dan, lay tu cell value
          (if (or (not comment-text) (= comment-text ""))
            (progn
              (setq cell-value nil)
              (setq value-result
                (vl-catch-all-apply
                  '(lambda ()
                     (setq cell-value (vlax-get-property active-cell "Value"))
                   )
                )
              )

              (if (and (not (vl-catch-all-error-p value-result)) cell-value)
                (progn
                  ;; Xu ly variant value
                  (if (= (type cell-value) 'VARIANT)
                    (setq cell-value (vlax-variant-value cell-value))
                  )
                  (setq cell-value (vl-princ-to-string cell-value))
                  (princ (strcat "\nCell value: " cell-value))
                  (setq comment-text cell-value)
                )
                (setq comment-text "")
              )
            )
          )

          ;; Tim duong dan file CAD
          (setq file-path (HTC-extract-file-path comment-text))

          ;; Kiem tra co duong dan hay khong
          (if (or (not file-path) (= file-path ""))
            (progn
              (princ "\nKhong tim thay duong dan file CAD trong comment hoac cell.")
              (vlax-release-object excel-app)
            )
            (progn
              (princ (strcat "\nDuong dan file: " file-path))

              ;; Kiem tra file co ton tai khong
              (if (findfile file-path)
                (progn
                  (princ "\nFile ton tai. Dang mo file...")

                  ;; Phuong phap 1: Dung VLA method (chinh)
                  (setq open-result
                    (vl-catch-all-apply
                      '(lambda ()
                         (setq acad-app (vlax-get-acad-object))
                         (setq acad-docs (vlax-get-property acad-app "Documents"))
                         (setq new-doc (vlax-invoke-method acad-docs "Open" file-path))

                         ;; Chuyen sang document moi
                         (if new-doc
                           (progn
                             ;; Cach 1: Set ActiveDocument
                             (vl-catch-all-apply
                               '(lambda ()
                                  (vlax-put-property acad-app "ActiveDocument" new-doc)
                                )
                             )

                             ;; Cach 2: Activate document
                             (vl-catch-all-apply
                               '(lambda ()
                                  (vlax-invoke-method new-doc "Activate")
                                )
                             )

                             ;; Cach 3: Set focus bang WindowState
                             (vl-catch-all-apply
                               '(lambda ()
                                  (vlax-put-property new-doc "WindowState" 3) ; acMax
                                )
                             )

                             (princ "\nDa mo va chuyen sang file moi.")
                           )
                           (princ "\nDa mo file nhung khong chuyen duoc sang file moi.")
                         )
                       )
                    )
                  )

                  ;; Neu VLA that bai, thu dung startapp
                  (if (vl-catch-all-error-p open-result)
                    (progn
                      (princ "\nVLA method that bai, thu dung startapp...")
                      (setq open-result2
                        (vl-catch-all-apply
                          '(lambda ()
                             ;; Mo bang cach goi AutoCAD moi
                             (startapp "acad.exe" file-path)
                             ;; Doi 2 giay roi chuyen focus
                             (command "delay" 2000)
                             (princ "\nDa mo file bang startapp.")
                           )
                        )
                      )

                      ;; Neu startapp cung that bai, thu command don gian
                      (if (vl-catch-all-error-p open-result2)
                        (progn
                          (princ "\nStartapp that bai, thu command don gian...")
                          (setq open-result3
                            (vl-catch-all-apply
                              '(lambda ()
                                 ;; Chuyen doi duong dan ve dinh dang 8.3 neu co the
                                 (setq short-path (vl-filename-mktemp file-path))
                                 (if short-path
                                   (command "_.open" short-path)
                                   (command "_.open" file-path)
                                 )
                               )
                            )
                          )
                        )
                      )
                    )
                  )

                  (princ (strcat "\nHoan thanh! Da thu mo file: " file-path))
                )
                (princ (strcat "\nLoi: File khong ton tai: " file-path))
              )

              (vlax-release-object excel-app)
            )
          )
        )
      )
    )
    (princ "\nLoi: Khong the ket noi den Excel.")
  )

  (princ)
)

;; Ham trich xuat duong dan file tu text
(defun HTC-extract-file-path (text / file-pos clean-path)
  (setq file-pos nil)
  (setq clean-path "")

  (if (and text (/= text ""))
    (progn
      ;; Tim vi tri "FileCad:"
      (setq file-pos (vl-string-search "FILECAD:" (strcase text)))

      (if file-pos
        (progn
          ;; Lay phan sau "FileCad:"
          (setq clean-path (substr text (+ file-pos 9))) ; 9 = length of "FileCad:" + 1

          ;; Loai bo khoang trang dau va cuoi
          (setq clean-path (vl-string-trim " \t\n\r" clean-path))

          ;; Loai bo cac ky tu xuong dong va khoang trang thua
          (setq clean-path (HTC-clean-file-path clean-path))

          (princ (strcat "\nDuong dan sau khi lam sach: " clean-path))
        )
        (setq clean-path "")
      )
    )
  )

  clean-path
)

;; Ham lam sach duong dan file
(defun HTC-clean-file-path (path / result i char)
  (setq result "")
  (setq i 1)

  (while (<= i (strlen path))
    (setq char (substr path i 1))

    ;; Chi giu lai cac ky tu hop le cho duong dan
    (if (or (and (>= char "A") (<= char "Z"))
            (and (>= char "a") (<= char "z"))
            (and (>= char "0") (<= char "9"))
            (= char "\\")
            (= char "/")
            (= char ":")
            (= char ".")
            (= char "_")
            (= char "-")
            (= char " ")
            (= char "#")
            (= char "(")
            (= char ")"))
      (setq result (strcat result char))
    )

    (setq i (1+ i))
  )

  ;; Loai bo khoang trang dau va cuoi lan nua
  (vl-string-trim " \t\n\r" result)
)

(princ "\nZoomToHandle.lsp loaded.")
(princ "\nCommands: ZTH, STH, HTC")
(princ)


;;---------------------------------------------o0o------------------------------------

; HNP_Cad Link Excel_CTE_fixed.lsp - Fixed for AutoCAD 2025+
; Phien ban sua loi cho AutoCAD 2025+ voi Express Tools loading

; Ham load Express Tools manh me cho AutoCAD 2025+
(defun c:load_acetutilARX (/ fn f n path)
  (if (not (member "acetutil.arx" (arx)))
    (progn
		(setq fn (findfile "acetutil.arx"))
		(if fn
			(progn
				(setq path (vl-filename-directory fn))
				(arxload fn)
				(if (findfile (strcat path "\\" "acetutil" ".fas"))
					(load (strcat path "\\" "acetutil" ".fas"))
				)
				(setq n 2)
				(repeat 3
					(setq f (strcat path "\\" "acetutil" (itoa n) ".fas"))
					(if (findfile f)
						(load f)
					)
					(setq n (1+ n))
				)
				(princ "\nExpress Tools ARX loaded successfully!")
			)
			(princ "\nWarning: acetutil.arx not found!")
		)
    )
	(princ "\nExpress Tools ARX already loaded.")
  )
)

; Load Express Tools cho AutoCAD 2025+ - Enhanced loading
(if (not (and (boundp 'acet-util-ver) acet-util-ver))
	(progn
		(princ "\nLoading Express Tools...")
		; Thu phuong phap 1: Load ARX va FAS files
		(c:load_acetutilARX)
		; Thu phuong phap 2: Load LSP files
		(if (findfile "acetutil.lsp")
			(load "acetutil.lsp")
		)
		; Thu phuong phap 3: Su dung acet-load-expresstools
		(if (not (and (boundp 'acet-util-ver) acet-util-ver))
			(vl-catch-all-apply 'acet-load-expresstools)
		)
		; Kiem tra ket qua
		(if (and (boundp 'acet-util-ver) acet-util-ver)
			(princ "\nExpress Tools loaded successfully!")
			(princ "\nWarning: Express Tools loading failed!")
		)
	)
	(princ "\nExpress Tools already loaded.")
)

; Ham test Express Tools
(defun c:test-express-tools ()
	(princ "\n=== EXPRESS TOOLS STATUS ===")
	(princ (strcat "\nacet-util-ver: " (if (and (boundp 'acet-util-ver) acet-util-ver) "LOADED" "NOT LOADED")))
	(princ (strcat "\nacet-explode: " (if (boundp 'acet-explode) "AVAILABLE" "NOT AVAILABLE")))
	; Test thu acet-explode
	(if (boundp 'acet-explode)
		(progn
			(princ "\nTesting acet-explode function...")
			(if (vl-catch-all-error-p (vl-catch-all-apply 'acet-explode '(nil)))
				(princ " - Function exists but needs valid input")
				(princ " - Function working")
			)
		)
	)
	(princ (strcat "\nARX modules: " (vl-princ-to-string (arx))))
	(princ "\n=============================")
	(princ)
)

; Ham debug CTE
(defun c:debug-cte-simple ()
	(princ "\n=== DEBUG CTE SIMPLE ===")
	; Test selection
	(setq ss (ssget '((0 . "TEXT,MTEXT"))))
	(if ss
		(progn
			(princ (strcat "\nSelected " (itoa (sslength ss)) " text objects"))
			(setq i 0)
			(repeat (sslength ss)
				(setq ent (ssname ss i))
				(setq obj (vlax-ename->vla-object ent))
				(setq type (vla-get-ObjectName obj))
				(princ (strcat "\nObject " (itoa (1+ i)) ": " type))
				(if (= type "AcDbMText")
					(progn
						(princ " - MTEXT detected")
						(if (boundp 'acet-explode)
							(princ " - acet-explode available")
							(princ " - acet-explode NOT available")
						)
					)
				)
				(setq i (1+ i))
			)
		)
		(princ "\nNo text objects selected")
	)
	(princ "\n=======================")
	(princ)
)

; Ham test MTEXT content extraction
(defun c:test-mtext-content ()
	(princ "\n=== TEST MTEXT CONTENT ===")
	(setq ss (ssget '((0 . "MTEXT"))))
	(if ss
		(progn
			(princ (strcat "\nSelected " (itoa (sslength ss)) " MTEXT objects"))
			(setq i 0)
			(repeat (sslength ss)
				(setq ent (ssname ss i))
				(setq obj (vlax-ename->vla-object ent))
				(princ (strcat "\n--- MTEXT " (itoa (1+ i)) " ---"))

				; Phuong phap 1: getpropertyvalue
				(setq content1 (getpropertyvalue ent "TEXT"))
				(princ (strcat "\nMethod 1 (getpropertyvalue): " content1))

				; Phuong phap 2: vla-get-TextString
				(setq content2 (vl-catch-all-apply 'vla-get-TextString (list obj)))
				(if (vl-catch-all-error-p content2)
					(setq content2 "ERROR")
				)
				(princ (strcat "\nMethod 2 (vla-get-TextString): " content2))

				; Phuong phap 3: CAEX_GET_LISTSTRINGCONTENT
				(setq content3 (CAEX_GET_LISTSTRINGCONTENT obj))
				(princ (strcat "\nMethod 3 (processed): " (vl-princ-to-string content3)))

				; Phuong phap 4: Clean MTEXT
				(setq content4 (CLEAN-MTEXT content2))
				(princ (strcat "\nMethod 4 (cleaned): " content4))

				(setq i (1+ i))
			)
		)
		(princ "\nNo MTEXT objects selected")
	)
	(princ "\n=========================")
	(princ)
)

(defun C:ETC ( /
	CheckActiveCell
	ListDataCoordinateX
	ListDataCoordinateY
	ListDataLine
	ListDataTable
	ListTemp
	ListVarSystem_OldValue
	ListVlaObjectOfTable
	NodeTotalX
	NodeTotalY
	ScaleGlobal
;	ScaleTable
	ScaleTableTemp
	VlaDrawingCurrent
	VlaSpace)

	-------------------------------------------------------------------------------------------------------------------
	
	(vl-load-com)
	(setq VlaDrawingCurrent (vla-get-activedocument (vlax-get-acad-object)))
	(vla-startundomark VlaDrawingCurrent)
	(CAEX_SET_VARSYSTEM_E2C)
	(CAEX_CREATE_LISTVLALAYERLOCK)

	(vl-catch-all-apply (function (lambda ( / )
		(setq CheckActiveCell (CAEX_CHECKACTIVECELL))
		(if CheckActiveCell
			(progn
				(if
					(not
						(and
							(or
								(= (type ScaleTable) 'INT)
								(= (type ScaleTable) 'REAL)
							)
							(> ScaleTable 0)
						)
					)
					(setq ScaleTable 1.0)
				)
				(initget 4)
				(setq ScaleTableTemp (getreal (strcat "\nEnter the scale of table <" (rtos ScaleTable 2) ">:")))
				(if ScaleTableTemp
					(setq ScaleTable ScaleTableTemp)
				)
				(if (= (getvar "CVPORT") 1)
					(setq VlaSpace (vla-get-PaperSpace VlaDrawingCurrent))
					(setq VlaSpace (vla-get-ModelSpace VlaDrawingCurrent))
				)

				(setq ListTemp (CAEX_GET_LISTDATATABLE_FROM_EXCEL Nil Nil))
				(setq ListDataTable (nth 0 ListTemp))
				(setq ListDataCoordinateX (nth 1 ListTemp))
				(setq ListDataCoordinateY (nth 2 ListTemp))

				(setq NodeTotalX (- (length ListDataCoordinateX) 1))
				(setq NodeTotalY (- (length ListDataCoordinateY) 1))
				(setq ScaleGlobal (* (/ 2.5 (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 6 x))) ListDataTable))) ScaleTable))

				(setq ListDataLine (CAEX_GET_LISTDATALINE_FROM_EXCEL))
				(setq ListVlaObjectOfTable (CAEX_CREATE_TABLE_FOR_CAD))
				(CAEX_PICK_POINT_FOR_TABLE)
			)
			(princ "\nData from excel could not be found!")
		)
	)))

	(CAEX_RESTORE_LOCK_LAYER)
	(CAEX_RESET_VARSYSTEM)
	(vla-endundomark VlaDrawingCurrent)
	(princ)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_PICK_POINT_FOR_TABLE ( / 
	CheckStop
	CodeMouse
	PointBase
	PointTemp
	Temp)

	(setq SeletionSet (ssadd))
	(foreach VlaObject ListVlaObjectOfTable
		(ssadd (vlax-vla-object->ename VlaObject) SeletionSet)
	)
	(command ".COPYBASE" (list 0.0 0.0 0.0) SeletionSet "")
	(mapcar 'vla-delete ListVlaObjectOfTable)
	(vl-catch-all-apply (function (lambda ( / )
		(command ".PASTECLIP" pause)
	)))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTDATALINE_FROM_EXCEL (  / 
	ListDataAddress
	ListNodeDash
	ListDataLine
	NodeDash
	NodeEndX
	NodeEndY
	NodeLine
	NodeStartX
	NodeStartY
	NodeX
	NodeY)

	(setq ListDataAddress (mapcar '(lambda (x) (nth 1 (assoc 1 x))) ListDataTable))
	(foreach ListAddress ListDataAddress
		(setq NodeX (+ (nth 0 ListAddress) 1))
		(repeat (- (nth 2 ListAddress) (nth 0 ListAddress))
			(setq NodeY (nth 1 ListAddress))
			(repeat (- (nth 3 ListAddress) (nth 1 ListAddress) -1)
				(setq NodeDash (list NodeX NodeY NodeX (+ NodeY 1)))
				(setq ListNodeDash (append ListNodeDash (list NodeDash)))
				(setq NodeY (+ NodeY 1))
			)
			(setq NodeX (+ NodeX 1))
		)

		(setq NodeY (+ (nth 1 ListAddress) 1))
		(repeat (- (nth 3 ListAddress) (nth 1 ListAddress))
			(setq NodeX (nth 0 ListAddress))
			(repeat (- (nth 2 ListAddress) (nth 0 ListAddress) -1)
				(setq NodeDash (list NodeX NodeY (+ NodeX 1) NodeY))
				(setq ListNodeDash (append ListNodeDash (list NodeDash)))
				(setq NodeX (+ NodeX 1))
			)
			(setq NodeY (+ NodeY 1))
		)
	)
	
	(setq ListDataLine
		(list
			(list 0 0 0 NodeTotalY)
			(list NodeTotalX 0 NodeTotalX NodeTotalY)
			(list 0 0 NodeTotalX 0)
			(list 0 NodeTotalY NodeTotalX NodeTotalY)
		)
	)

	(setq NodeX 1)
	(repeat (- NodeTotalX 1)
		(setq NodeY 0)
		(setq NodeStartY NodeY)
		(repeat NodeTotalY
			(setq NodeDash (list NodeX NodeY NodeX (+ NodeY 1)))
			(if (member NodeDash ListNodeDash)
				(progn
					(setq NodeEndY NodeY)
					(if (/= NodeStartY NodeEndY)
						(progn
							(setq NodeLine (list NodeX NodeStartY NodeX NodeEndY))
							(setq ListDataLine (append ListDataLine (list NodeLine)))
						)
					)
					(setq NodeStartY (+ NodeY 1))
				)
			)
			(setq NodeY (+ NodeY 1))
		)
		(setq NodeEndY NodeY)
		(if (/= NodeStartY NodeEndY)
			(progn
				(setq NodeLine (list NodeX NodeStartY NodeX NodeEndY))
				(setq ListDataLine (append ListDataLine (list NodeLine)))
			)
		)
		(setq NodeX (+ NodeX 1))
	)

	(setq NodeY 1)
	(repeat (- NodeTotalY 1)
		(setq NodeX 0)
		(setq NodeStartX NodeX)
		(repeat NodeTotalX
			(setq NodeDash (list NodeX NodeY (+ NodeX 1) NodeY))
			(if (member NodeDash ListNodeDash)
				(progn
					(setq NodeEndX NodeX)
					(if (/= NodeStartX NodeEndX)
						(progn
							(setq NodeLine (list NodeStartX NodeY NodeEndX NodeY))
							(setq ListDataLine (append ListDataLine (list NodeLine)))
						)
					)
					(setq NodeStartX (+ NodeX 1))
				)
			)
			(setq NodeX (+ NodeX 1))
		)
		(setq NodeEndX NodeX)
		(if (/= NodeStartX NodeEndX)
			(progn
				(setq NodeLine (list NodeStartX NodeY NodeEndX NodeY))
				(setq ListDataLine (append ListDataLine (list NodeLine)))
			)
		)
		(setq NodeY (+ NodeY 1))
	)
	ListDataLine
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTDATATABLE_FROM_EXCEL ( NumColTotal NumRowTotal / 
	CheckCloseApp
	CheckHiddenCol
	CheckHiddenRow
	HeightRow
	ListAddressTotal
	ListDataCoordinateY
	ListDataCoordinateX
	ListDataTable
	NumCol
	NumColStart
	NumRow
	NumRowStart
	StringAddress
	VlaAppExcel
	VlaRange
	VlaRangeCol
    VlaRangeRow
	VlaSheet
	VlaWorkbooks
	WidthColumn)

	(setq VlaAppExcel (vlax-get-or-create-Object "Excel.Application"))
	(vlax-put-property VlaAppExcel "Visible" :vlax-true)
	(setq VlaWorkbooks (vlax-get-property VlaAppExcel "Workbooks"))
	(setq CheckCloseApp (= (vla-get-count VlaWorkbooks) 0))
	(setq VlaRange (vlax-get-property VlaAppExcel "Selection"))
	(setq VlaSheet (vlax-get-property VlaAppExcel "ActiveSheet"))

	(if VlaRange
		(progn
			(setq StringAddress (vlax-get-property VlaRange "Address" :vlax-false :vlax-false 1 :vlax-false :vlax-false))
			(setq ListAddressTotal (CAEX_STRINGADDRESS_TO_LISTADDRESS StringAddress))
			(if (= (length ListAddressTotal) 2)
				(setq ListAddressTotal (append ListAddressTotal ListAddressTotal))
			)
			(if (not NumColTotal)
				(setq NumColTotal (- (nth 2 ListAddressTotal) (nth 0 ListAddressTotal) -1))
			)
			(if (not NumRowTotal)
				(setq NumRowTotal (- (nth 3 ListAddressTotal) (nth 1 ListAddressTotal) -1))
			)
			(setq NumColStart (nth 0 ListAddressTotal))
			(setq NumRowStart (nth 1 ListAddressTotal))

			(setq NumRow NumRowStart)
			(repeat NumRowTotal
				(setq NumCol NumColStart)
				(repeat NumColTotal
					(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS (list NumCol NumRow)))
					(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
					(setq VlaRangeRow (vlax-get-property VlaRange "Rows"))
					(setq CheckHiddenRow (vlax-variant-value (vlax-get-property VlaRangeRow "Hidden")))
					(setq VlaRangeCol (vlax-get-property VlaRange "Columns"))
					(setq CheckHiddenCol (vlax-variant-value (vlax-get-property VlaRangeCol "Hidden")))
					(if
						(and
							(= CheckHiddenRow :vlax-false)
							(= CheckHiddenCol :vlax-false)
						)
						(CAEX_ADD_LISTDATATABLE_FROM_EXCEL VlaRange)
					)
					(setq NumCol (+ NumCol 1))
				)
				(setq NumRow (+ NumRow 1))
			)

			(setq NumCol NumColStart)
			(setq ListDataCoordinateX (list 0.0))
			(repeat NumColTotal
				(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS (list NumCol 0)))
				(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
				(setq VlaRange (vlax-get-property VlaRange "Columns"))
				(setq WidthColumn (variant-value (vlax-get-property VlaRange "ColumnWidth")))
				(setq ListDataCoordinateX (append ListDataCoordinateX (list (+ (last ListDataCoordinateX) WidthColumn))))
				(setq NumCol (+ NumCol 1))
			)

			(setq NumRow NumRowStart)
			(setq ListDataCoordinateY (list 0.0))
			(repeat NumRowTotal
				(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS (list 0 NumRow)))
				(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
				(setq VlaRange (vlax-get-property VlaRange "Rows"))
				(setq HeightRow (variant-value (vlax-get-property VlaRange "RowHeight")))
				(setq ListDataCoordinateY (append ListDataCoordinateY (list (+ (last ListDataCoordinateY) HeightRow))))
				(setq NumRow (+ NumRow 1))
			)
		)
	)

	(if CheckCloseApp
		(progn
			(vlax-invoke-method VlaAppExcel "Quit")
			(vlax-release-object VlaAppExcel)
		)
	)
	(list ListDataTable ListDataCoordinateX ListDataCoordinateY)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_ADD_LISTDATATABLE_FROM_EXCEL ( VlaRange / 
	Alignment
	DataTable
	ListAlignment
	ListNameProperty
	ListAddressMerge
	ListValueProperty
	ListValuePropertyTemp
	ListStringContent
	FontBold
	FontItalic
	FontName
	HeightText
	Num
	Rotation
	StringAddressMerge
	StringContent
	VlaFont
	VlaRangeMerge
	ValueProperty)

	(setq VlaRangeMerge (vlax-get-property VlaRange "MergeArea"))
	(setq StringAddressMerge (vlax-get-property VlaRangeMerge "Address" :vlax-false :vlax-false 1 :vlax-false :vlax-false))
	(setq ListAddressMerge (CAEX_STRINGADDRESS_TO_LISTADDRESS StringAddressMerge))
	(if (= (length ListAddressMerge) 2)
		(setq ListAddressMerge (append ListAddressMerge ListAddressMerge))
	)
	(setq ListAddressMerge
		(list
			(- (nth 0 ListAddressMerge) NumColStart)
			(- (nth 1 ListAddressMerge) NumRowStart)
			(- (nth 2 ListAddressMerge) NumColStart)
			(- (nth 3 ListAddressMerge) NumRowStart)
		)
	)

	(if (not (assoc (list 1 ListAddressMerge) ListDataTable))
		(progn
			(setq StringContent (vlax-variant-value (vlax-get-property VlaRange "Text")))
			(setq ListStringContent (CAEX_STRING_TO_LIST_NEW StringContent "\n"))
			(setq VlaFont (vlax-get-property VlaRange "Font"))
			(setq ListNameProperty (list "Name" "Bold" "Italic" "Size"))
			(foreach NameProperty ListNameProperty
				(setq ValueProperty (vlax-variant-value (vlax-get-property VlaFont NameProperty)))
				(if (not ValueProperty)
					(progn
						(setq ListValuePropertyTemp Nil)
						(setq Num 1)
						(repeat (strlen StringContent)
							(setq VlaCharacters (vlax-get-property VlaRange "Characters" Num 1))
							(setq VlaFontChar (vlax-get-property VlaCharacters "Font"))
							(setq ValuePropertyTemp (vlax-variant-value (vlax-get-property VlaFontChar NameProperty)))
							(setq ListValuePropertyTemp (cons ValuePropertyTemp ListValuePropertyTemp))
							(setq Num (+ Num 1))
						)
						(setq ValueProperty (CAEX_FIND_VALUE_POPULAR ListValuePropertyTemp))
					)
				)
				(setq ListValueProperty (append ListValueProperty (list ValueProperty)))
			)
			(setq FontName (nth 0 ListValueProperty))
			(setq FontBold (nth 1 ListValueProperty))
			(setq FontItalic (nth 2 ListValueProperty))
			(setq HeightText (nth 3 ListValueProperty))

			(setq Rotation (vlax-variant-value (vlax-get-property VlaRange "Orientation")))
			(cond
				((= Rotation -4128)
					(setq Rotation 0.0)
				)
				((= Rotation -4171)
					(setq Rotation 90.0)
				)
				((= Rotation -4170)
					(setq Rotation -90.0)
				)
				((= Rotation -4166)
					(setq Rotation -90.0)
				)
			)
			(setq Rotation (CAEX_ROUNDOFF_NUMBER Rotation 90.0))
			(setq Rotation (* (/ Rotation 180.0) Pi))

			(if (= Rotation 0.0)
				(progn
					(setq ListAlignment
						(list
							(cons -4108 "Center")
							(cons 7 "Center")
							(cons -4117 "Center")
							(cons 5 "Center")
							(cons 1 "Center")
							(cons -4130 "Center")
							(cons -4131 "Left")
							(cons -4152 "Right")
						)
					)
					(setq Alignment (vlax-variant-value (vlax-get-property VlaRange "HorizontalAlignment")))
					(setq Alignment (cdr (assoc Alignment ListAlignment)))
					(setq Rotation 0.0)
				)
				(progn
					(setq ListAlignment
						(list
							(cons -4107 "Bottom")
							(cons -4108 "Center")
							(cons -4117 "Center")
							(cons -4130 "Center")
							(cons -4160 "Top")
						)
					)
					(setq Alignment (vlax-variant-value (vlax-get-property VlaRange "VerticalAlignment")))
					(setq Alignment (cdr (assoc Alignment ListAlignment)))
				)
			)

			(setq DataTable
				(list
					(list 1 ListAddressMerge)
					(list 2 ListStringContent)
					(list 3 FontName)
					(list 4 FontBold)
					(list 5 FontItalic)
					(list 6 HeightText)
					(list 7 Alignment)
					(list 8 Rotation)
				)
			)
			(setq ListDataTable (append ListDataTable (list DataTable)))
		)
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CREATE_TABLE_FOR_CAD ( /
	Alignment
	CodeAlignment
	DeltaX
	DeltaY
	FontBold
	FontItalic
	FontName
	GapText
	HeightText
	LengthMax
	LengthMaxX
	LengthMaxY
	LengthText
	LengthTextX
	LengthTextY
	ListAddress
	ListDataBoundary
	ListStringContent
	ListVlaObjectOfTable
	ListVlaObjectTextNew
	NameTextStyle
	NumText
	Point1
	Point2
	PointMinX
	PointMinY
	PointMaxX
	PointMaxY
	Point1Pre
	Point2Pre
	PointText
	PointTextBase
	PointTextTemp
	PointTextX
	PointTextY
	Rotation
	ValueScaleText
	VlaObjectLine
	VlaObjectText)

	(foreach NodeLine ListDataLine
		(setq Point1
			(list
				(CAEX_NODEX_TO_POINTX (nth 0 NodeLine))
				(CAEX_NODEY_TO_POINTY (nth 1 NodeLine))
				0.0
			)
		)
		(setq Point2
			(list
				(CAEX_NODEX_TO_POINTX (nth 2 NodeLine))
				(CAEX_NODEY_TO_POINTY (nth 3 NodeLine))
				0.0
			)
		)
		(if
			(or
				(not (equal Point1 Point1Pre))
				(not (equal Point2 Point2Pre))
            )
			(progn
				(setq VlaObjectLine (vla-addline VlaSpace (vlax-3d-point Point1) (vlax-3d-point Point2)))
				(setq ListVlaObjectOfTable (cons VlaObjectLine ListVlaObjectOfTable))
				(setq Point1Pre Point1)
				(setq Point2Pre Point2)
            )
        )
	)

	(foreach DataTable ListDataTable
		(setq ListAddress (nth 1 (assoc 1 DataTable)))
		(setq ListStringContent (nth 1 (assoc 2 DataTable)))
		(setq FontName (nth 1 (assoc 3 DataTable)))
		(setq FontBold (nth 1 (assoc 4 DataTable)))
		(setq FontItalic (nth 1 (assoc 5 DataTable)))
		(setq HeightText (* (nth 1 (assoc 6 DataTable)) ScaleGlobal))
		(setq Alignment (nth 1 (assoc 7 DataTable)))
		(setq Rotation (nth 1 (assoc 8 DataTable)))

		(setq PointMinX (CAEX_NODEX_TO_POINTX (nth 0 ListAddress)))
		(setq PointMinY (CAEX_NODEY_TO_POINTY (+ (nth 3 ListAddress) 1)))
		(setq PointMaxX (CAEX_NODEX_TO_POINTX (+ (nth 2 ListAddress) 1)))
		(setq PointMaxY (CAEX_NODEY_TO_POINTY (nth 1 ListAddress)))

		(setq NameTextStyle (CAEX_FIND_NAMETEXTSTYLE FontName FontBold FontItalic))
		(setq ListVlaObjectTextNew Nil)

		(setq NumText (length ListStringContent))
		(if (= Rotation 0.0)
			(progn
				(setq GapText (/ (- PointMaxY PointMinY (* NumText HeightText)) (+ NumText 1)))
				(cond
					((= Alignment "Left")
						(setq PointTextX (+ PointMinX (* HeightText 0.5)))
						(setq CodeAlignment acAlignmentBottomLeft)
					)
					((= Alignment "Center")
						(setq PointTextX (+ PointMinX (* (- PointMaxX PointMinX) 0.5)))
						(setq CodeAlignment acAlignmentBottomCenter)
					)
					((= Alignment "Right")
						(setq PointTextX (- PointMaxX (* HeightText 0.5)))
						(setq CodeAlignment acAlignmentBottomRight)
					)
				)
				(setq PointTextY (- PointMaxY GapText (* HeightText 1.0)))
				(setq PointText (list PointTextX PointTextY 0.0))
				(foreach StringContent ListStringContent
					(if (/= StringContent "")
						(progn
							(setq VlaObjectText (vla-AddText VlaSpace StringContent (vlax-3d-point PointText) HeightText))
							(setq ListVlaObjectTextNew (cons VlaObjectText ListVlaObjectTextNew))
							(setq ListVlaObjectOfTable (cons VlaObjectText ListVlaObjectOfTable))
							(vla-put-alignment VlaObjectText CodeAlignment)
							(setq PointTextTemp
								(list
									(nth 0 PointText)
									(- (nth 1 PointText) (nth 1 (vlax-safearray->list (vlax-variant-value (vla-get-InsertionPoint VlaObjectText)))))
									(nth 2 PointText)
								)
							)
							(vla-put-TextAlignmentPoint VlaObjectText (vlax-3d-point PointTextTemp))
							(vla-put-StyleName VlaObjectText NameTextStyle)
						)
					)
					(setq PointTextY (- PointTextY GapText HeightText))
					(setq PointText (list PointTextX PointTextY 0.0))
				)
			)
			(progn
				(setq GapText (/ (- PointMaxX PointMinX (* NumText HeightText)) (+ NumText 1)))
				(cond
					((= Alignment "Bottom")
						(progn
							(setq PointTextY (+ PointMinY (* HeightText 0.5)))
							(if (= Rotation (* Pi 0.5))
								(setq CodeAlignment acAlignmentBottomLeft)
								(setq CodeAlignment acAlignmentBottomRight)
							)
						)
					)
					((= Alignment "Center")
						(setq PointTextY (+ PointMinY (* (- PointMaxY PointMinY) 0.5)))
						(setq CodeAlignment acAlignmentBottomCenter)
					)
					((= Alignment "Top")
						(progn
							(setq PointTextY (- PointMaxY (* HeightText 0.5)))
							(if (= Rotation (* Pi 0.5))
								(setq CodeAlignment acAlignmentBottomRight)
								(setq CodeAlignment acAlignmentBottomLeft)
							)
						)
					)
				)
				(if (= Rotation (* Pi 0.5))
					(progn
						(setq PointTextX (+ PointMinX GapText (* HeightText 1.0)))
					)
					(progn
						(setq ListStringContent (reverse ListStringContent))
						(setq PointTextX (+ PointMinX GapText (* HeightText 0.0)))
					)
				)
				(setq PointText (list PointTextX PointTextY 0.0))
				(foreach StringContent ListStringContent
					(if (/= StringContent "")
						(progn
							(setq VlaObjectText (vla-AddText VlaSpace StringContent (vlax-3d-point PointText) HeightText))
							(setq ListVlaObjectTextNew (cons VlaObjectText ListVlaObjectTextNew))
							(setq ListVlaObjectOfTable (cons VlaObjectText ListVlaObjectOfTable))
							(vla-put-Rotation VlaObjectText Rotation)
							(vla-put-alignment VlaObjectText CodeAlignment)
							(setq PointTextTemp
								(list
									(- (nth 0 PointText) (nth 0 (vlax-safearray->list (vlax-variant-value (vla-get-InsertionPoint VlaObjectText)))))
									(nth 1 PointText)
									(nth 2 PointText)
								)
							)
							(vla-put-TextAlignmentPoint VlaObjectText (vlax-3d-point PointTextTemp))
							(vla-put-StyleName VlaObjectText NameTextStyle)
						)
					)
					(setq PointTextX (+ PointTextX GapText HeightText))
					(setq PointText (list PointTextX PointTextY 0.0))
				)
			)
		)

		(setq ListDataBoundary (mapcar 'CAEX_GET_POINTMIN_POINTMAX_TEXT_MTEXT ListVlaObjectTextNew))
		(setq ListDataBoundary (vl-remove ListDataBoundary Nil))
		(if ListDataBoundary
			(progn
				(setq LengthTextX
					(-
						(apply 'max (mapcar '(lambda (x) (nth 0 (nth 1 x))) ListDataBoundary))
						(apply 'min (mapcar '(lambda (x) (nth 0 (nth 0 x))) ListDataBoundary))
					)
				)
				(setq LengthTextY
					(-
						(apply 'max (mapcar '(lambda (x) (nth 1 (nth 1 x))) ListDataBoundary))
						(apply 'min (mapcar '(lambda (x) (nth 1 (nth 0 x))) ListDataBoundary))
					)
				)
			)
			(progn
				(setq LengthTextX 0.0)
				(setq LengthTextY 0.0)
			)
		)
		(setq LengthMaxX (- PointMaxX PointMinX (* HeightText 0.1)))
		(setq DeltaX (- LengthMaxX LengthTextX))

		(setq LengthMaxY (- PointMaxY PointMinY (* HeightText 0.1)))
		(setq DeltaY (- LengthMaxY LengthTextY))
		(if
			(or
				(< DeltaX 0.0)
				(< DeltaY 0.0)
			)
			(progn
				(if (< DeltaX DeltaY)
					(progn
						(setq LengthMax LengthMaxX)
						(setq LengthText LengthTextX)
					)
					(progn
						(setq LengthMax LengthMaxY)
						(setq LengthText LengthTextY)
					)
				)
				(setq ValueScaleText (/ LengthMax LengthText))

				(if (= Rotation 0.0)
					(progn
						(cond
							((= Alignment "Left")
								(setq PointTextBase
									(list
										PointMinX
										(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
										0.0
									)
								)
							)
							((= Alignment "Center")
								(setq PointTextBase
									(list
										(+ PointMinX (* (- PointMaxX PointMinX) 0.5))
										(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
										0.0
									)
								)
							)
							((= Alignment "Right")
								(setq PointTextBase
									(list
										PointMaxX
										(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
										0.0
									)
								)
							)
						)
					)
					(progn
						(cond
							((= Alignment "Left")
								(setq PointTextBase
									(list
										PointMinX
										(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
										0.0
									)
								)
							)
							((= Alignment "Center")
								(setq PointTextBase
									(list
										(+ PointMinX (* (- PointMaxX PointMinX) 0.5))
										(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
										0.0
									)
								)
							)
							((= Alignment "Right")
								(setq PointTextBase
									(list
										PointMaxX
										(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
										0.0
									)
								)
							)
						)
					)
				)

				(foreach VlaObjectText ListVlaObjectTextNew
					(vla-ScaleEntity VlaObjectText (vlax-3d-point PointTextBase) ValueScaleText)
				)
			)
		)
	)
	ListVlaObjectOfTable
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_FIND_NAMETEXTSTYLE ( FontName FontBold FontItalic / 
	Charset
	NameTextStyle
	PitchAndFamily
	VlaTextStyle
	VlaTextStylesGroup)

	(setq NameTextStyle (strcat "CLE-" (strcase FontName)))
	(if (= FontBold :vlax-true)
		(setq NameTextStyle (strcat NameTextStyle "-B"))
	)
	(if (= FontItalic :vlax-true)
		(setq NameTextStyle (strcat NameTextStyle "-I"))
	)
	(setq VlaTextStylesGroup (vla-get-textstyles VlaDrawingCurrent))
	(vl-catch-all-apply (function (lambda ( / )
		(setq VlaTextStyle (vla-item VlaTextStylesGroup NameTextStyle))
	)))
	(if (not VlaTextStyle)
		(setq VlaTextStyle (vla-add VlaTextStylesGroup NameTextStyle))
	)
	(setq Charset 1)
	(setq PitchAndFamily 0)

	(if
		(vl-catch-all-error-p (vl-catch-all-apply (function (lambda ( / )
			(vla-SetFont VlaTextStyle FontName FontBold FontItalic Charset PitchAndFamily)
		))))
		(progn
			(setq FontName "Arial")
			(vla-SetFont VlaTextStyle FontName FontBold FontItalic Charset PitchAndFamily)
		)
	)
	(vla-put-Height VlaTextStyle 0.0)
	(vla-put-width VlaTextStyle 0.7)
	(CAEX_PUT_ANNOTATIVE_TEXTSTYLE VlaTextStyle Nil)
	(CAEX_PUT_ORIENTATION_TEXTSTYLE VlaTextStyle Nil)
	NameTextStyle
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_PUT_ANNOTATIVE_TEXTSTYLE ( VlaTextStyle ModeAnnotative / EnameTextStyle )
	(setq EnameTextStyle (vlax-vla-object->ename VlaTextStyle))
	(if ModeAnnotative
		(entmod (list (cons -1 EnameTextStyle) (list -3 (list "AcadAnnotative" (cons 1000 "AnnotativeData") (cons 1002 "{") (cons 1070 1) (cons 1070 1) (cons 1002 "}")))))
		(entmod (list (cons -1 EnameTextStyle) (list -3 (cons "AcadAnnotative" Nil))))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_PUT_ORIENTATION_TEXTSTYLE ( VlaObject ModeOrientation / EnameTextStyle)

	(setq EnameTextStyle (vlax-vla-object->ename VlaObject))
	(if ModeOrientation
		(entmod (list (cons -1 EnameTextStyle) (list -3 (cons "AcadAnnoPO" (list (cons 1070 1))))))
		(entmod (list (cons -1 EnameTextStyle) (list -3 (cons "AcadAnnoPO" Nil))))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_NODEX_TO_POINTX ( NodeX / PointX)
	(setq PointX (+ 0.0 (* (nth NodeX ListDataCoordinateX) ScaleGlobal 11.64)))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_NODEY_TO_POINTY ( NodeY / PointY)
	(setq PointY (- 0.0 (* (nth NodeY ListDataCoordinateY) ScaleGlobal 1.91)))
)
-------------------------------------------------------------------------------------------------------------------
(defun C:CTE ( / 
	CheckActiveCell
	CheckRun
	FullNameFileXlsx
	GroupListAddress
	ListCoordinateX
	ListCoordinateY
	ListDataLineX
	ListDataLineY
	ListDataTable
	ListTemp
	ListVarSystem_OldValue
	ListVlaLayerLock
	ListVlaObjectLine
	ListVlaObjectDelete
	ListVlaObjectText
	SeparatorCSV
	ToleranceValue
	VlaDrawingCurrent)

	-------------------------------------------------------------------------------------------------------------------
	
	(vl-load-com)
	(setq VlaDrawingCurrent (vla-get-activedocument (vlax-get-acad-object)))
	(vla-startundomark VlaDrawingCurrent)
	(setq ToleranceValue 1e-8)
	(CAEX_SET_VARSYSTEM_C2E)
	(CAEX_CREATE_LISTVLALAYERLOCK)

	(vl-catch-all-apply (function (lambda ( / )
		(setq SeparatorCSV (vl-registry-read "HKEY_CURRENT_USER\\Control Panel\\International" "sList"))
		(if (not SeparatorCSV)
			(setq SeparatorCSV ",")
		)

		(setq ListTemp (CAEX_SELECT_TABLE_IN_CAD))
		(setq ListVlaObjectLine (nth 0 ListTemp))
		(setq ListVlaObjectText (nth 1 ListTemp))
		(setq ListVlaObjectDelete (nth 2 ListTemp))

		(if
			(and
				ListVlaObjectLine
				ListVlaObjectText
			)
			(progn
				(setq CheckActiveCell (CAEX_CHECKACTIVECELL))
				(if CheckActiveCell
					(setq CheckRun T)
					(progn
						(setq FullNameFileXlsx (getfiled "Select Output File" (CAEX_GET_FULLNAMEFILEXLSX "xlsx") "xlsx" 1))
						(if FullNameFileXlsx
							(setq CheckRun T)
						)
					)
				)
			)
		)

		(if CheckRun
			(progn
				(setq ListTemp (CAEX_GET_LISTDATALINE_FROM_CAD))
				(setq ListDataLineX (nth 0 ListTemp))
				(setq ListDataLineY (nth 1 ListTemp))
				(setq ListCoordinateX (mapcar 'car ListDataLineX))
				(setq ListCoordinateY (mapcar 'car ListDataLineY))

				(setq GroupListAddress (CAEX_GET_GROUPLISTADDRESS_FROM_CAD))
				(setq ListDataTable (CAEX_GET_LISTDATATABLE_FROM_CAD))
				(CAEX_CREATE_TABLE_FOR_EXCEL)
			)
		)
	)))

	(mapcar 'vla-delete ListVlaObjectDelete)
	(CAEX_RESTORE_LOCK_LAYER)
	(CAEX_RESET_VARSYSTEM)
	(vla-endundomark VlaDrawingCurrent)
	(princ)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CHECKACTIVECELL ( / 
	CheckActiveCell
	CheckCloseApp
	VlaAppExcel
	VlaRangeActive
	VlaWorkbooks)

	(setq VlaAppExcel (vlax-get-Object "Excel.Application"))
	(if VlaAppExcel
		(progn
			(setq VlaRangeActive (vlax-get-property VlaAppExcel "Selection"))
			(if VlaRangeActive
				(setq CheckActiveCell T)
			)

			(setq VlaWorkbooks (vlax-get-property VlaAppExcel "Workbooks"))
			(setq CheckCloseApp (= (vla-get-count VlaWorkbooks) 0))
			(if CheckCloseApp
				(progn
					(vlax-invoke-method VlaAppExcel "Quit")
					(vlax-release-object VlaAppExcel)
				)
			)
		)
	)
	CheckActiveCell
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_SELECT_TABLE_IN_CAD ( / 
	ListVlaObjectLine
	ListVlaObjectDelete
	ListVlaObjectSelect
	ListVlaObjectTemp
	ListVlaObjectText
	SelectionFilter
	SelectionSet
	SelectionSetTemp
	TypeObject
	VlaObjectCopy)

	(setq SelectionFilter
		(list
			(cons -4 "<OR")
			(cons 0 "LINE")
			(cons 0 "LWPOLYLINE")
			(cons 0 "TEXT")
			(cons 0 "MTEXT")
			(cons -4 "OR>")
		)
	)
	(setq SelectionSet (ssget SelectionFilter))
	(setq ListVlaObjectSelect (CAEX_CONVERT_SELECTIONSET_TO_LISTVLAOBJECT SelectionSet))
	(foreach VlaObjectSelect ListVlaObjectSelect
		(setq TypeObject (vla-get-ObjectName VlaObjectSelect))
		(if (= TypeObject "AcDbPolyline")
			(progn
				(setq VlaObjectCopy (vla-copy VlaObjectSelect))
				; Kiem tra acet-explode co san khong
				(if (boundp 'acet-explode)
					(progn
						(setq SelectionSetTemp (acet-explode (vlax-vla-object->ename VlaObjectCopy)))
						(setq ListVlaObjectTemp (CAEX_CONVERT_SELECTIONSET_TO_LISTVLAOBJECT SelectionSetTemp))
						(foreach VlaObjectTemp ListVlaObjectTemp
							(if (= (vla-get-ObjectName VlaObjectTemp) "AcDbLine")
								(setq ListVlaObjectLine (cons VlaObjectTemp ListVlaObjectLine))
							)
						)
						(setq ListVlaObjectDelete (append ListVlaObjectDelete ListVlaObjectTemp))
					)
					(progn
						; Fallback: bo qua LWPOLYLINE neu khong co acet-explode
						(princ "\nWarning: acet-explode not available, skipping LWPOLYLINE")
						(vla-delete VlaObjectCopy)
					)
				)
			)
		)
		(if (= TypeObject "AcDbLine")
			(setq ListVlaObjectLine (cons VlaObjectSelect ListVlaObjectLine))
		)

		(if
			(or
				(= TypeObject "AcDbText")
				(= TypeObject "AcDbMText")
			)
			(setq ListVlaObjectText (cons VlaObjectSelect ListVlaObjectText))
		)
	)
	(list ListVlaObjectLine ListVlaObjectText ListVlaObjectDelete)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_FULLNAMEFILEXLSX ( Extension / 
	FullNameFileDwg
	FullNameFileXlsx
	NameFileXlsx
	PathFileXlsx)

	(setq FullNameFileDwg (vla-get-fullname VlaDrawingCurrent))
	(setq PathFileXlsx (vl-filename-directory FullNameFileDwg))
	(setq NameFileXlsx (strcat (vl-filename-base FullNameFileDwg) "." Extension))
	(setq FullNameFileXlsx (strcat PathFileXlsx "\\" NameFileXlsx))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTDATALINE_FROM_CAD ( /
	ListDataLineY
	ListDataLineX
	PointEnd
	PointStart
	PointX
	Temp1
	Temp2)

	(foreach VlaObject ListVlaObjectLine
		(setq PointStart (vlax-curve-getStartPoint VlaObject))
		(setq PointEnd (vlax-curve-getEndPoint VlaObject))
		(if (= (CAEX_ROUNDOFF_NUMBER (sin (vla-get-angle VlaObject)) 0.05) 0.0)
			(setq ToleranceValue (max ToleranceValue (abs (- (nth 1 PointStart) (nth 1 PointEnd)))))
		)
		(if (= (CAEX_ROUNDOFF_NUMBER (cos (vla-get-angle VlaObject)) 0.05) 0.0)
			(setq ToleranceValue (max ToleranceValue (abs (- (nth 0 PointStart) (nth 0 PointEnd)))))
		)
	)
	(setq ToleranceValue (* (expt 10.0 (CAEX_ROUNDOFF_NUMBER (/ (log ToleranceValue) (log 10.0)) 1.0)) 2.0))

	(foreach VlaObject ListVlaObjectLine
		(setq PointStart (vlax-curve-getStartPoint VlaObject))
		(setq PointEnd (vlax-curve-getEndPoint VlaObject))
		(setq PointStart (mapcar '(lambda (x) (CAEX_ROUNDOFF_NUMBER x ToleranceValue)) PointStart))
		(setq PointEnd (mapcar '(lambda (x) (CAEX_ROUNDOFF_NUMBER x ToleranceValue)) PointEnd))

		(if (= (CAEX_ROUNDOFF_NUMBER (cos (vla-get-angle VlaObject)) 0.05) 0.0)
			(progn
				(setq PointX (nth 0 PointStart))
				(setq Temp2 (vl-sort (list (nth 1 PointStart) (nth 1 PointEnd)) '<))
				(if (setq Temp1 (assoc PointX ListDataLineX))
					(setq ListDataLineX (subst (append Temp1 (list Temp2)) Temp1 ListDataLineX))
					(setq ListDataLineX (append ListDataLineX (list (list PointX Temp2))))
				)
			)
		)

		(if (= (CAEX_ROUNDOFF_NUMBER (sin (vla-get-angle VlaObject)) 0.05) 0.0)
			(progn
				(setq PointY (nth 1 PointStart))
				(setq Temp2 (vl-sort (list (nth 0 PointStart) (nth 0 PointEnd)) '<))
				(if (setq Temp1 (assoc PointY ListDataLineY))
					(setq ListDataLineY (subst (append Temp1 (list Temp2)) Temp1 ListDataLineY))
					(setq ListDataLineY (append ListDataLineY (list (list PointY Temp2))))
				)
			)
		)
	)

	(setq ListDataLineX (vl-sort ListDataLineX '(lambda (a b) (< (car a) (car b)))))
	(setq ListDataLineY (vl-sort ListDataLineY '(lambda (a b) (> (car a) (car b)))))
	(list ListDataLineX ListDataLineY)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_GROUPLISTADDRESS_FROM_CAD ( / 
	Cell
	Cell2
	CheckMerge
	GroupListAddress
	NodeCell
	NodeCellMerge
	NodeCellTemp
	NumCol
	NumColTotal
	NumRow
	NumRowTotal
	ListCoordinateX
	ListCoordinateY
	ListNodeCell
	ListNodeCell1
	ListNodeCell2
	ListNodeCellHorizontal
	ListNodeCellTemp
	ListNodeCellVertical
	ListNumCol
	ListNumRow
	PointX
	PointY)

	(setq ListCoordinateX (mapcar 'car ListDataLineX))
	(setq ListCoordinateY (mapcar 'car ListDataLineY))
	(setq NumColTotal (- (length ListCoordinateX) 1))
	(setq NumRowTotal (- (length ListCoordinateY) 1))

	(setq NumCol 1)
	(repeat NumColTotal
		(setq NumRow 1)
		(setq NodeCell (list (cons NumCol NumRow)))
		(setq PointX (* (+ (nth (- NumCol 1) ListCoordinateX) (nth NumCol ListCoordinateX)) 0.5))
		(repeat (- NumRowTotal 1)
			(setq PointY (nth NumRow ListCoordinateY))
			(setq CheckMerge T)
			(foreach Temp (cdr (assoc PointY ListDataLineY))
				(if
					(and
						(> PointX (nth 0 Temp))
						(< PointX (nth 1 Temp))
					)
					(setq CheckMerge Nil)
				)
			)
			(setq NumRow (+ NumRow 1))
			(if CheckMerge
				(setq NodeCell (append NodeCell (list (cons NumCol NumRow))))
				(progn
					(if (> (length NodeCell) 1)
						(setq ListNodeCellVertical (append ListNodeCellVertical (list NodeCell)))
					)
					(setq NodeCell (list (cons NumCol NumRow)))
				)
			)
		)
		(if (> (length NodeCell) 1)
			(setq ListNodeCellVertical (append ListNodeCellVertical (list NodeCell)))
		)
		(setq NumCol (+ NumCol 1))
	)

	(setq NumRow 1)
	(repeat NumRowTotal
		(setq NumCol 1)
		(setq NodeCell (list (cons NumCol NumRow)))
		(setq PointY (* (+ (nth (- NumRow 1) ListCoordinateY) (nth NumRow ListCoordinateY)) 0.5))
		(repeat (- NumColTotal 1)
			(setq PointX (nth NumCol ListCoordinateX))
			(setq CheckMerge T)
			(foreach Temp (cdr (assoc PointX ListDataLineX))
				(if
					(and
						(> PointY (nth 0 Temp))
						(< PointY (nth 1 Temp))
					)
					(setq CheckMerge Nil)
				)
			)
			(setq NumCol (+ NumCol 1))
			(if CheckMerge
				(setq NodeCell (append NodeCell (list (cons NumCol NumRow))))
				(progn
					(if (> (length NodeCell) 1)
						(setq ListNodeCellHorizontal (append ListNodeCellHorizontal (list NodeCell)))
					)
					(setq NodeCell (list (cons NumCol NumRow)))
				)
			)
		)
		(if (> (length NodeCell) 1)
			(setq ListNodeCellHorizontal (append ListNodeCellHorizontal (list NodeCell)))
		)
		(setq NumRow (+ NumRow 1))
	)

	(setq ListNodeCell ListNodeCellHorizontal)
	(setq ListNodeCell1 ListNodeCellVertical)
	(foreach NodeCell1 ListNodeCell1
		(setq NodeCellMerge NodeCell1)
		(setq ListNodeCell2 ListNodeCell)
		(foreach NodeCell2 ListNodeCell2
			(setq CheckMerge (CAEX_CHECK_MERGE_NODECELL NodeCell1 NodeCell2))
			(if CheckMerge
				(progn
					(foreach Cell2 NodeCell2
						(if (not (member Cell2 NodeCellMerge))
							(setq NodeCellMerge (append NodeCellMerge (list Cell2)))
						)
					)
					(setq ListNodeCell (vl-remove NodeCell2 ListNodeCell))
				)
			)
		)
		(setq ListNodeCell (append ListNodeCell (list NodeCellMerge)))
	)

	(setq NodeCellTemp (apply 'append ListNodeCell))
	(setq NumCol 1)
	(repeat NumColTotal
		(setq NumRow 1)
		(repeat NumRowTotal
			(setq Cell (cons NumCol NumRow))
			(if (not (member Cell NodeCellTemp))
				(setq ListNodeCell (append ListNodeCell (list (list Cell))))
			)
			(setq NumRow (+ NumRow 1))
		)
		(setq NumCol (+ NumCol 1))
	)

	(setq ListNodeCellTemp ListNodeCell)
	(setq ListNodeCell Nil)
	(foreach NodeCell ListNodeCellTemp
		(setq ListNumCol (vl-sort (mapcar 'car NodeCell) '<))
		(setq ListNumRow (vl-sort (mapcar 'cdr NodeCell) '<))
		(setq NodeCell
			(list
				(cons (car ListNumCol) (car ListNumRow))
				(cons (last ListNumCol) (last ListNumRow))
			)
		)
		(setq ListNodeCell (append ListNodeCell (list NodeCell)))
	)

	(setq GroupListAddress (mapcar '(lambda (x) (list (- (car (nth 0 x)) 1) (- (cdr (nth 0 x)) 1) (- (car (nth 1 x)) 1) (- (cdr (nth 1 x)) 1))) ListNodeCell))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CHECK_MERGE_NODECELL ( NodeCell1 NodeCell2 / CheckMerge)
	(foreach Cell2 NodeCell2
		(if (member Cell2 NodeCell1)
			(setq CheckMerge T)
		)
	)
	CheckMerge
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTDATATABLE_FROM_CAD ( / 
	Alignment
	DistanceAdd
	DistanceDiv
	DistanceSub
	Charset
	FontBold
	FontItalic                
	HeightText
	ListDataTable
	GroupListAddressEmpty
	ListTemp
	ListVlaObjectTextSelect
	PitchAndFamily
	PointMaxX
	PointMaxY
	PointMinX
	PointMinY
	PointTextMax
	PointTextMaxX
	PointTextMaxY
	PointTextMin
	PointTextMinX
	PointTextMinY
	FontName
	NameTextStyle
	Rotation
	SelectionFilter
	SelectionFrame
	SelectionSet
	StringContent
	VlaTextStyle)


	(foreach ListAddress GroupListAddress
		(setq ListVlaObjectTextSelect (CAEX_GET_LISTVLATEXT_IN_CELL ListAddress))
		(if ListVlaObjectTextSelect
			(progn
				(setq PointMinX (nth (nth 0 ListAddress) ListCoordinateX))
				(setq PointMaxY (nth (nth 1 ListAddress) ListCoordinateY))
				(setq PointMaxX (nth (+ (nth 2 ListAddress) 1) ListCoordinateX))
				(setq PointMinY (nth (+ (nth 3 ListAddress) 1) ListCoordinateY))

				(setq ListTemp (CAEX_GET_STRINGCONTENT_MULTIOBJECT ListVlaObjectTextSelect))
				(setq StringContent (nth 0 ListTemp))
				(setq PointTextMin (nth 1 ListTemp))
				(setq PointTextMax (nth 2 ListTemp))
				(setq PointTextMinX (nth 0 PointTextMin))
				(setq PointTextMaxX (nth 0 PointTextMax))
				(setq PointTextMinY (nth 1 PointTextMin))
				(setq PointTextMaxY (nth 1 PointTextMax))

				(setq NameTextStyle (CAEX_FIND_VALUE_POPULAR (mapcar 'CAEX_VLA_GET_STYLENAME ListVlaObjectTextSelect)))
				(setq VlaTextStyle (vla-item (vla-get-textstyles VlaDrawingCurrent) NameTextStyle))
				(vla-GetFont VlaTextStyle 'FontName 'FontBold 'FontItalic 'Charset 'PitchAndFamily)
				(if
					(or
						(not FontName)
						(= FontName "")
					)
					(setq FontName "Arial")
				)
				(setq HeightText (CAEX_FIND_VALUE_POPULAR (mapcar 'vla-get-height ListVlaObjectTextSelect)))

				(setq Rotation (CAEX_FIND_VALUE_POPULAR (mapcar 'vla-get-rotation ListVlaObjectTextSelect)))
				(setq Rotation (* (/ Rotation Pi) 180.0))
				(setq Rotation (CAEX_ROUNDOFF_NUMBER Rotation 90.0))
				(if (> Rotation 90.0)
					(setq Rotation (- Rotation 180.0))
				)

				(if (= Rotation 0.0)
					(progn
						(if (= (- PointMaxX PointTextMaxX) 0.0)
							(setq DistanceDiv (/ (- PointTextMinX PointMinX) ToleranceValue))
							(setq DistanceDiv (/ (- PointTextMinX PointMinX) (- PointMaxX PointTextMaxX)))
						)
						(setq DistanceAdd (+ (- PointTextMinX PointMinX) (- PointMaxX PointTextMaxX)))
						(setq DistanceSub (abs (- (- PointTextMinX PointMinX) (- PointMaxX PointTextMaxX))))
						(if (= DistanceSub 0.0)
							(setq DistanceSub ToleranceValue)
						)
						(setq Alignment Nil)
						(if (>= DistanceDiv 10.0)
							(setq Alignment "Right")
						)
						(if (<= DistanceDiv 1.0)
							(setq Alignment "Left")
						)
						(if (>= (/ DistanceAdd DistanceSub) 5.0)
							(setq Alignment "Center")
						)
						(if (not Alignment)
							(setq Alignment (CAEX_FIND_VALUE_POPULAR (mapcar 'CAEX_FIND_ALIGNMENT_TEXT_MTEXT ListVlaObjectTextSelect)))
						)
					)
					(progn
						(if (= (- PointMaxY PointTextMaxY) 0.0)
							(setq DistanceDiv (/ (- PointTextMinY PointMinY) ToleranceValue))
							(setq DistanceDiv (/ (- PointTextMinY PointMinY) (- PointMaxY PointTextMaxY)))
						)
						(setq DistanceAdd (+ (- PointMaxY PointTextMaxY) (- PointTextMinY PointMinY)))
						(setq DistanceSub (abs (- (- PointTextMinY PointMinY) (- PointMaxY PointTextMaxY))))
						(if (= DistanceSub 0.0)
							(setq DistanceSub ToleranceValue)
						)
						(setq Alignment Nil)
						(if (>= DistanceDiv 10.0)
							(setq Alignment "Top")
						)
						(if (<= DistanceDiv 0.1)
							(setq Alignment "Bottom")
						)
						(if (>= (/ DistanceAdd DistanceSub) 5.0)
							(setq Alignment "Center")
						)
						(if (not Alignment)
							(setq Alignment (CAEX_FIND_VALUE_POPULAR (mapcar 'CAEX_FIND_ALIGNMENT_TEXT_MTEXT ListVlaObjectTextSelect)))
						)
					)
				)

				(setq ListDataTable
					(cons
						(list
							(list 0 (list (nth 0 ListAddress) (nth 1 ListAddress)))
							(list 1 ListAddress)
							(list 2 StringContent)
							(list 3 FontName)
							(list 4 FontBold)
							(list 5 FontItalic)
							(list 6 HeightText)
							(list 7 Alignment)
							(list 8 Rotation)
							(list 9 ListVlaObjectTextSelect)
						)
						ListDataTable
					)
				)
			)
			(setq GroupListAddressEmpty (append GroupListAddressEmpty (list ListAddress)))
		)
	)

	(if GroupListAddressEmpty
		(progn
			(setq StringContent "")
			(setq FontName (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 3 (cdr x)))) ListDataTable)))
			(setq FontBold (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 4 (cdr x)))) ListDataTable)))
			(setq FontItalic (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 5 (cdr x)))) ListDataTable)))
			(setq HeightText (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 6 (cdr x)))) ListDataTable)))
			(setq Alignment (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 7 (cdr x)))) ListDataTable)))
			(setq Rotation (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 8 (cdr x)))) ListDataTable)))
			(if
				(or
					(not FontName)
					(= FontName "")
				)
				(setq FontName "Arial")
			)
			(if (not FontBold)
				(setq FontBold :vlax-false)
			)
			(if (not FontItalic)
				(setq FontItalic :vlax-false)
			)
			(if (not HeightText)
				(setq HeightText 12.0)
			)
			(if (not Alignment)
				(setq Alignment "Center")
			)
			(if (not Rotation)
				(setq Rotation 0.0)
			)
			(foreach ListAddress GroupListAddressEmpty
				(setq ListDataTable
					(cons
						(list
							(list 0 (list (nth 0 ListAddress) (nth 1 ListAddress)))
							(list 1 ListAddress)
							(list 2 StringContent)
							(list 3 FontName)
							(list 4 FontBold)
							(list 5 FontItalic)
							(list 6 HeightText)
							(list 7 Alignment)
							(list 8 Rotation)
							(list 9 Nil)
						)
						ListDataTable
					)
				)
			)
		)
	)
	ListDataTable
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTVLATEXT_IN_CELL ( ListAddress /
	ListTemp
	ListVlaObjectTextSelect
	ListVlaTextTemp
	P_MaxX1
	P_MaxX2
	P_MaxY1
	P_MaxY2
	P_MinX1
	P_MinX2
	P_MinY1
	P_MinY2
	SelectionFilter
	SelectionFrame
	SelectionSet)

	(setq SelectionFilter
		(list
			(cons -4 "<OR")
			(cons 0 "TEXT")
			(cons 0 "MTEXT")
			(cons -4 "OR>")
		)
	)
	(setq P_MinX1 (nth (nth 0 ListAddress) ListCoordinateX))
	(setq P_MaxY1 (nth (nth 1 ListAddress) ListCoordinateY))
	(setq P_MaxX1 (nth (+ (nth 2 ListAddress) 1) ListCoordinateX))
	(setq P_MinY1 (nth (+ (nth 3 ListAddress) 1) ListCoordinateY))
	(setq SelectionFrame
		(list
			(list P_MinX1 P_MinY1)
			(list P_MaxX1 P_MinY1)
			(list P_MaxX1 P_MaxY1)
			(list P_MinX1 P_MaxY1)
		)
	)

	(setq SelectionSet (ssget "_CP" SelectionFrame SelectionFilter))
	(setq ListVlaTextTemp (CAEX_CONVERT_SELECTIONSET_TO_LISTVLAOBJECT SelectionSet))
	(foreach VlaTextTemp ListVlaTextTemp
		(if (member VlaTextTemp ListVlaObjectText)
			(progn
				(setq ListTemp (CAEX_GET_POINTMIN_POINTMAX_TEXT_MTEXT VlaTextTemp))
				(setq P_MinX2 (nth 0 (nth 0 ListTemp)))
				(setq P_MinY2 (nth 1 (nth 0 ListTemp)))
				(setq P_MaxX2 (nth 0 (nth 1 ListTemp)))
				(setq P_MaxY2 (nth 1 (nth 1 ListTemp)))
				(if
					(and
						(<= P_MinX1 P_MinX2)
						(>= P_MaxX1 P_MaxX2)
						(<= P_MinY1 P_MinY2)
						(>= P_MaxY1 P_MaxY2)
					)
					(setq ListVlaObjectTextSelect (cons VlaTextTemp ListVlaObjectTextSelect))
				)
			)
		)
	)
	ListVlaObjectTextSelect
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CREATE_TABLE_FOR_EXCEL ( / 
	DataTable
	FileCsv
	FullNameFileCsv
	ListAddress
	ListDataTableRaw
	ListStringContent
	NumCol
	NumColTotal
	NumRow
	NumRowTotal
	StringContent
	StringValue)

	(setq NumColTotal (- (length ListCoordinateX) 1))
	(setq NumRowTotal (- (length ListCoordinateY) 1))

	(setq NumRow 0)
	(repeat NumRowTotal
		(setq NumCol 0)
		(setq ListStringContent Nil)
		(repeat NumColTotal
			(setq ListAddress (list NumCol NumRow))
			(if (setq DataTable (assoc (list 0 ListAddress) ListDataTable))
				(setq StringContent (nth 1 (assoc 2 DataTable)))
				(setq StringContent "")
			)
			(setq ListStringContent (append ListStringContent (list StringContent)))
			(setq NumCol (+ NumCol 1))
		)
		(setq ListDataTableRaw (append ListDataTableRaw (list ListStringContent)))
		(setq NumRow (+ NumRow 1))
	)

	(setq FullNameFileCsv (strcat (getvar "TEMPPREFIX") (CAEX_RANDOM_STRING 8) ".csv"))
	(while (CAEX_CHECK_FILE_EXIST FullNameFileCsv)
		(setq FullNameFileCsv (strcat (getvar "TEMPPREFIX") (CAEX_RANDOM_STRING 8) ".csv"))
	)

	(setq FileCsv (open FullNameFileCsv "w"))
	(setq StringValue (strcat "sep=" SeparatorCsv))
	(write-line StringValue FileCsv)

	(setq StringValue "")
	(write-line StringValue FileCsv)
	(foreach DataTableRaw ListDataTableRaw
		(setq DataTableRaw (cons "" DataTableRaw))
		(setq StringValue (CAEX_LIST_TO_STRING DataTableRaw SeparatorCsv))
		(write-line StringValue FileCsv)
	)
	(close FileCsv)

	(CAEX_PROCESS_CSV)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_PROCESS_CSV ( / 
	Alignment
	ColumnWidth
	DataTable
	FontBold
	FontItalic
	HeightText
	ListAddress
	ListAlignment
	ListFontProperty
	FontName
	NameSheet
	NumCol
	NumColStart
	NumColTotal
	NumRow
	NumRowStart
	NumRowTotal
	Rotation
	RowHeight
	ScaleGlobal
	StringAddress
	VlaAppExcel
	VlaBorders
	VlaFont
	VlaRange
	VlaRangeActive
	VlaSheet
	VlaSheets
	VlaWorkbook
	VlaWorkbooks)

	(setq NumColTotal (- (length ListCoordinateX) 1))
	(setq NumRowTotal (- (length ListCoordinateY) 1))

	(setq VlaAppExcel (vlax-get-or-create-Object "Excel.Application"))
	(if CheckActiveCell
		(progn
			(vlax-put-property VlaAppExcel "Visible" :vlax-true)
			(setq VlaSheetActive (vlax-get-property VlaAppExcel "ActiveSheet"))
			(setq VlaRangeActive (vlax-get-property VlaAppExcel "Selection"))
			(setq StringAddress (vlax-get-property VlaRangeActive "Address" :vlax-false :vlax-false 1 :vlax-false :vlax-false))
			(setq ListAddress (CAEX_STRINGADDRESS_TO_LISTADDRESS StringAddress))
			(setq NumColStart (nth 0 ListAddress))
			(setq NumRowStart (nth 1 ListAddress))

			(setq ListAddress (list NumColStart NumRowStart (+ NumColStart NumColTotal -1) (+ NumRowStart NumRowTotal -1)))
			(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
			(setq VlaRangeActive (vlax-get-property VlaSheetActive "Range" StringAddress))
			(vlax-put-property VlaRangeActive "MergeCells" 0)
			(vlax-invoke-method VlaRangeActive "ClearFormats")
		)
		(progn
			(vlax-put-property VlaAppExcel "Visible" :vlax-false)
			(setq NumColStart 1)
			(setq NumRowStart 1)
		)
	)
	(setq VlaWorkbooks (vlax-get-property VlaAppExcel "Workbooks"))
	(setq VlaWorkbook (vlax-invoke-method VlaWorkbooks "Open" FullNameFileCsv))
	(setq VlaSheets (vlax-get-property VlaWorkbook "Sheets"))
	(setq VlaSheet (vlax-get-property VlaSheets "Item" 1))

	(if CheckActiveCell
		(progn
			(setq VlaRange (vlax-get-property VlaSheet "Range" (CAEX_LISTADDRESS_TO_STRINGADDRESS (list 1 1 NumColTotal NumRowTotal))))
			(vlax-invoke-method VlaRange "Copy")
			(vlax-invoke-method VlaRangeActive "PasteSpecial" -4163 -4142 :vlax-false :vlax-false)
			(vlax-put-property VlaAppExcel "CutCopyMode" :vlax-false)
			(vlax-invoke-method VlaWorkbook "Save")
			(vlax-invoke-method VlaWorkbook "Close")
			(setq VlaSheet VlaSheetActive)
		)

		(progn
			(if
				(vl-catch-all-error-p (vl-catch-all-apply (function (lambda ( / )
					(setq NameSheet (vl-filename-base FullNameFileXlsx))
					(vlax-put-property VlaSheet "Name" NameSheet)
				))))
				(progn
					(setq NameSheet "Data Cad")
					(vlax-put-property VlaSheet "Name" NameSheet)
				)
			)

			(setq VlaRange (vlax-get-property VlaSheet "Cells"))
			(vlax-invoke-method VlaRange "ClearFormats")
			(vlax-put-property VlaRange "VerticalAlignment" -4108)
			(vlax-put-property VlaRange "HorizontalAlignment" -4108)
			(setq VlaFont (vlax-get-property VlaRange "Font"))
			(setq ListFontProperty
				(list
					(cons "Background" Nil)
					(cons "Bold" 0)
					(cons "Color" 0.0)
					(cons "ColorIndex" 1)
					(cons "FontStyle" "Regular")
					(cons "Italic" 0)
					(cons "Name" "Arial")
					(cons "Size" 12)
					(cons "Strikethrough" 0)
					(cons "Subscript" 0)
					(cons "ThemeColor" 2)
					(cons "ThemeFont" 0)
					(cons "TintAndShade" 0.0)
					(cons "Underline" -4142)
				)
			)
			(foreach FontProperty ListFontProperty
				(vlax-put-property VlaFont (car FontProperty) (cdr FontProperty))
			)
			(vlax-put-property VlaRange "RowHeight" 25)
			(vlax-put-property VlaRange "ColumnWidth" 30)

			(setq VlaRange (vlax-get-property VlaSheet "Range" (CAEX_LISTADDRESS_TO_STRINGADDRESS (list 1 1 NumColTotal NumRowTotal))))
			(vlax-invoke-method VlaRange "Copy")
			(vlax-invoke-method VlaRange "PasteSpecial" -4163 -4142 :vlax-false :vlax-false)
			(vlax-put-property VlaAppExcel "CutCopyMode" :vlax-false)
		)
	)

	(setq ListAddress (list NumColStart NumRowStart (+ NumColStart NumColTotal -1) (+ NumRowStart NumRowTotal -1)))
	(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
	(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
	(vlax-put-property VlaRange "VerticalAlignment" -4108)
	(vlax-put-property VlaRange "HorizontalAlignment" -4108)

	(setq VlaBorders (vlax-get-property VlaRange "Borders"))
	(foreach BordersIndex (list 7 8 9 10 11 12)
		(setq VlaBorder (vlax-get-property VlaBorders "Item" BordersIndex))
		(vlax-put-property VlaBorder "LineStyle" 1)
		(vlax-put-property VlaBorder "Weight" 2)
	)

	(setq ScaleGlobal (/ 12.0 (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 6 x))) (mapcar 'cdr ListDataTable)))))
	(foreach DataTable ListDataTable
		(setq ListAddress (nth 1 (assoc 1 DataTable)))
		(setq FontName (nth 1 (assoc 3 DataTable)))
		(setq FontBold (nth 1 (assoc 4 DataTable)))
		(setq FontItalic (nth 1 (assoc 5 DataTable)))
		(setq HeightText (* (nth 1 (assoc 6 DataTable)) ScaleGlobal))
		(setq Alignment (nth 1 (assoc 7 DataTable)))
		(setq Rotation (nth 1 (assoc 8 DataTable)))

		(setq ListAddress (list (+ NumColStart (nth 0 ListAddress)) (+ NumRowStart (nth 1 ListAddress)) (+ NumColStart (nth 2 ListAddress)) (+ NumRowStart (nth 3 ListAddress))))
		(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
		(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))

		(if (= Rotation 0.0)
			(progn
				(setq ListAlignment
					(list
						(cons "Center" -4108)
						(cons "Left" -4131)
						(cons "Right" -4152)
					)
				)
				(setq Alignment (cdr (assoc Alignment ListAlignment)))
				(vlax-put-property VlaRange "HorizontalAlignment" Alignment)
			)
			(progn
				(setq ListAlignment
					(list
						(cons "Top" -4160)
						(cons "Center" -4108)
						(cons "Bottom" -4107)
					)
				)
				(setq Alignment (cdr (assoc Alignment ListAlignment)))
				(vlax-put-property VlaRange "VerticalAlignment" Alignment)
			)
		)
		(setq VlaFont (vlax-get-property VlaRange "Font"))
		(vlax-put-property VlaFont "Name" FontName)
		(vlax-put-property VlaFont "Bold" FontBold)
		(vlax-put-property VlaFont "Italic" FontItalic)
		(vlax-put-property VlaFont "Size" HeightText)
		(vlax-put-property VlaRange "Orientation" Rotation)
		(vlax-put-property VlaRange "MergeCells" 1)
		(vlax-put-property VlaRange "WrapText" 1)
	)

	(setq NumCol 0)
	(repeat NumColTotal
		(setq ListAddress (list (+ NumColStart NumCol) NumRowStart (+ NumColStart NumCol) (+ NumRowStart NumRowTotal)))
		(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
		(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
		(setq VlaRange (vlax-get-property VlaRange "Columns"))
		(setq ColumnWidth (/ (* (- (nth (+ NumCol 1) ListCoordinateX) (nth NumCol ListCoordinateX)) ScaleGlobal) 11.64))
		(vlax-put-property VlaRange "ColumnWidth" ColumnWidth)
		(setq NumCol (+ NumCol 1))
	)

	(setq NumRow 0)
	(repeat NumRowTotal
		(setq ListAddress (list NumColStart (+ NumRowStart NumRow) (+ NumColStart NumColTotal) (+ NumRowStart NumRow)))
		(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
		(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
		(setq VlaRange (vlax-get-property VlaRange "Rows"))
		(setq RowHeight (/ (* (- (nth NumRow ListCoordinateY) (nth (+ NumRow 1) ListCoordinateY)) ScaleGlobal) 1.91))
		(vlax-put-property VlaRange "RowHeight" RowHeight)
		(setq NumRow (+ NumRow 1))
	)

	(if (not CheckActiveCell)
		(progn
			(vl-catch-all-apply (function (lambda ( / )
				(vl-file-delete FullNameFileXlsx)
				(vlax-invoke-method VlaWorkbook "SaveAs" FullNameFileXlsx 51 nil nil :vlax-false :vlax-false 1 2)
			)))
			(princ (strcat "\nExported data to file \"" FullNameFileXlsx "\"!"))
		)
	)

	(vlax-put-property VlaAppExcel "Visible" :vlax-true)
	(vl-file-delete FullNameFileCsv)
)
-------------------------------------------------------------------------------------------------------------------
(defun C:UPDATEEXCELFROMCAD ( / 
	CheckActiveCell
	GroupListAddress
	GroupListAddressUpdate
	ListCoordinateX
	ListCoordinateY
	ListDataCell
	ListDataLineX
	ListDataLineY
	ListDataTable
	ListTemp
	ListVarSystem_OldValue
	ListVlaLayerLock
	ListVlaObjectDelete
	ListVlaObjectLine
	ListVlaObjectText
	NameLayerCell
	ToleranceValue
	VlaDrawingCurrent
	VlaLayersGroup
	VlaSpace)

	-------------------------------------------------------------------------------------------------------------------
	(defun LIC_REQUESTCODE ( / 
		CheckLicense
		CodeSoftware
		DateCurrent
		DateUsed
		ListDataUser
		ListCharTotal
		ListNumHash
		ListSerialNumTotal
		ListSerialString
		ListStringQuery
		NameSoftware
		NumCharTotal
		NumCodeSoftware
		NumRequestCode
		NumHash
		NumSeedMax
		NumSeed
		NumUsed
		Pos
		LicenseKey
		LicenseKeyExact
		RequestCode
		UserName)

		(setq NameSoftware "Cad Link Excel")
		(defun LIC_LOAD_DIALOG ( NameSoftware /
			End_Main_DCL
			Main_DCL
			LicenseKey)

			(defun LIC_GET_TILE_LICENSENUMBER ( / )
				(setq LicenseKey (vl-string-trim " " (get_tile "Tile_LicenseNumber")))
				(setq ListNumHash (vl-string->list RequestCode))
				(setq NumSeed (rem (apply '+ ListNumHash) NumSeedMax))
				(setq LicenseKeyExact "")
				(foreach NumHash ListNumHash
					(setq LicenseKeyExact (strcat LicenseKeyExact (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
				)
				(if (= LicenseKeyExact LicenseKey)
					(progn
						(vl-registry-write (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "License Key" LicenseKey)
						(set_tile "Tile_ActivateText" "The official version has been activated!")
						(mode_tile "Tile_ActivateText" 1)
						(mode_tile "Tile_LicenseNumber" 1)
					)
					(progn
						(set_tile "Tile_ActivateText" "License number is incorrect!")
						(mode_tile "Tile_ActivateText" 1)
						(mode_tile "Tile_LicenseNumber" 0)
					)
				)
				(setq LicenseKeyExact Nil)
			)

			(LIC_MAKE_FILE_DCL NameSoftware)
			(setq Main_DCL (load_dialog (strcat NameSoftware " License.dcl")))
			(new_dialog (strcat (LIC_REMOVE_SPACE_OF_STRING NameSoftware) "License") Main_DCL)

			(setq LicenseKey "")
			(LIC_SET_TILE_DECORATION 4)
			(set_tile "Tile_RequestCode" RequestCode)

			(action_tile "Tile_LicenseNumber" "(LIC_GET_TILE_LICENSENUMBER)")
			(action_tile "Tile_CopyRequestCode" "(vlax-invoke (vlax-get (vlax-get (vlax-create-object \"HTMLFile\") 'ParentWindow) 'ClipBoardData) 'setData \"Text\" RequestCode)")

			(setq End_Main_DCL (start_dialog))
			(cond
				(
					(or
						(= End_Main_DCL 0)
						(= End_Main_DCL 1)
					)
					(unload_dialog Main_DCL)
				)
			)
			(setq LIC_GET_TILE_LICENSENUMBER Nil)
			(princ)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_MAKE_FILE_DCL ( NameSoftware /
			Num
			DclFile
			DirectoryDes)

			(setq DirectoryDes (strcat (getvar "roamablerootprefix") "Support"))
			(setq DclFile (open (strcat DirectoryDes "\\" NameSoftware " License.dcl") "w"))
			(write-line "///------------------------------------------------------------------------" DclFile)
			(write-line (strcat "///		 " NameSoftware " License.dcl") DclFile)
			(write-line (strcat (LIC_REMOVE_SPACE_OF_STRING NameSoftware) "License:dialog{") DclFile)
			(write-line (strcat "label = \"" NameSoftware " License\";") DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"Tile_ActivateText\";" DclFile)
			(write-line "	alignment = centered;" DclFile)
			(write-line "	width = 60;" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep0\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "		:column{" DclFile)
			(write-line "		width = 60;" DclFile)

			(write-line "			:text{" DclFile)
			(write-line "				label = \"     You are using the trial version of this app.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     If you find it useful, you can pay 5 USD per license.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     Then you will not see this message board every time you use the application.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     <NAME_EMAIL> (+84 933 648 160) .\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     Thank you for using and supporting.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "		}" DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep1\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:row{" DclFile)
			(write-line "		:text{" DclFile)
			(write-line "		label = \"     Request code\";" DclFile)
			(write-line "		width = 15;" DclFile)
			(write-line "		}" DclFile)

			(write-line "		:text{" DclFile)
			(write-line "		key = \"Tile_RequestCode\";" DclFile)
			(write-line "		width = 45;" DclFile)
			(write-line "		}" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep2\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:row{" DclFile)
			(write-line "		:text{" DclFile)
			(write-line "		label = \"     Enter license\";" DclFile)
			(write-line "		width = 15;" DclFile)
			(write-line "		}" DclFile)

			(write-line "		:edit_box{" DclFile)
			(write-line "		key = \"Tile_LicenseNumber\";" DclFile)
			(write-line "		width = 45;" DclFile)
			(write-line "		}" DclFile)
			(write-line "	}" DclFile)  
		
			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep3\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:row{" DclFile)
			(write-line "		:button{" DclFile)
			(write-line "		label = \"&Continue\";" DclFile)
			(write-line "		key = \"Ok\";" DclFile)
			(write-line "		is_default = true;" DclFile)
			(write-line "		is_cancel = true;" DclFile)
			(write-line "		width = 30;" DclFile)
			(write-line "		}" DclFile)

			(write-line "		:button{" DclFile)
			(write-line "		label = \"Copy &request code\";" DclFile)
			(write-line "		key = \"Tile_CopyRequestCode\";" DclFile)
			(write-line "		width = 30;" DclFile)
			(write-line "		}" DclFile)
			(write-line "	}" DclFile)
			(write-line "}" DclFile)
			(close DclFile)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_SET_TILE_DECORATION ( NumTotal / Num Tile)
			(setq Num 0)
			(repeat NumTotal
				(setq Tile (strcat "sep" (itoa Num)))
				(set_tile Tile "--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------")
				(setq Num (+ Num 1))
			)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_REMOVE_SPACE_OF_STRING ( String / )
			(while (/= String (setq StringTemp (vl-string-subst "" " " String)))
				(setq String StringTemp)
			)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_GETSERIALNUMBER ( StringQuery StringNameSerial / 
			SerialNumber
			VlaObjectLocal
			VlaObjectServive
			VlaObjectSet)

			(setq VlaObjectLocal (vlax-create-object "WbemScripting.SWbemLocator"))
			(setq VlaObjectServive (vlax-invoke VlaObjectLocal 'ConnectServer nil nil nil nil nil nil nil nil))
			(setq Server (vlax-invoke VlaObjectLocal 'ConnectServer "." "root\\cimv2"))
			(setq VlaObjectSet
				(vlax-invoke 
					VlaObjectServive
					"ExecQuery"
					StringQuery
				)
			)
			(vlax-for VlaObject VlaObjectSet
				(setq SerialNumber (vlax-get VlaObject StringNameSerial))
			)
			SerialNumber
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_SEND_REQUESTAPI ( StringUrl / 
			CodeStatus
			StringResponse
			VlaHttpRequest)

			(vl-catch-all-apply (function (lambda ( / )
				(setq VlaHttpRequest (vlax-invoke-method (vlax-get-acad-object) 'GetInterfaceObject "WinHttp.WinHttpRequest.5.1"))
				(vlax-invoke-method VlaHttpRequest 'Open "GET" StringUrl :vlax-false)
				(vlax-invoke-method VlaHttpRequest 'Send)
				(setq CodeStatus (vlax-get-property VlaHttpRequest 'Status))
				(if (= CodeStatus 200)
					(setq StringResponse (vlax-get-property VlaHttpRequest 'ResponseText))
				)
			)))
			StringResponse
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_SEND_REQUESTAPI_SENDDATAUSER ( ListData / 
			StringResponse
			StringUrl)

			(vl-catch-all-apply (function (lambda ( / )
				(setq StringUrl "https://script.google.com/macros/s/AKfycbz1n3v780vBayl68zGEGeEc0swT9HgO1Sg5c_bz4xK-tO9oUARczwj59oH4UCPcgify/exec?")
				(foreach Data ListData
					(setq StringUrl (strcat StringUrl (car Data) "=" (cdr Data) "&"))
				)
				(setq StringResponse (LIC_SEND_REQUESTAPI StringUrl))
			)))
			StringResponse
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_GET_CURRENT_DATE ( / )
			(setq StringTemp (rtos (getvar "CDATE") 2 6))
			(strcat (substr StringTemp 1 4) "-" (substr StringTemp 5 2) "-" (substr StringTemp 7 2))
		)
		-------------------------------------------------------------------------------------------------------------------
		(vl-load-com)
		(setq ListStringQuery
			(list
				(cons "Select * from Win32_BaseBoard" "SerialNumber")
				(cons "Select * from Win32_BIOS" "SerialNumber")
			)
		)
		(setq ListSerialString (cons NameSoftware (mapcar '(lambda (x) (LIC_GETSERIALNUMBER (car x) (cdr x))) ListStringQuery)))
		(setq ListSerialString (vl-remove Nil ListSerialString))
		(setq ListSerialNumTotal (mapcar 'vl-string->list ListSerialString))
		(setq LIC_GETSERIALNUMBER Nil)

		(setq ListCharTotal (list "A" "B" "C" "D" "E" "F" "G" "H" "I" "J" "K" "L" "M" "N" "O" "P" "Q" "R" "S" "T" "U" "V" "W" "X" "Y" "Z" "0" "1" "2" "3" "4" "5" "6" "7" "8" "9"))
		(setq NumCharTotal (length ListCharTotal))
		(setq NumSeedMax 100000000)

		(setq ListNumHash (vl-string->list NameSoftware))
		(setq NumSeed (rem (apply '+ ListNumHash) NumSeedMax))
		(setq CodeSoftware "")
		(setq NumCodeSoftware 6)
		(setq Pos 0)
		(while (< (strlen CodeSoftware) NumCodeSoftware)
			(setq NumHash (nth 0 ListNumHash))
			(if (not NumHash)
				(setq NumHash NumSeed)
			)
			(setq CodeSoftware (strcat CodeSoftware (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
		)

		(setq RequestCode CodeSoftware)
		(setq Pos 0)
		(setq NumRequestCode 36)
		(while (< (strlen RequestCode) NumRequestCode)
			(foreach ListSerialNum ListSerialNumTotal
				(setq NumHash Nil)
				(vl-catch-all-apply (function (lambda ( / )
					(setq NumHash (nth Pos ListSerialNum))
				)))
				(if (not NumHash)
					(setq NumHash NumSeed)
				)
				(setq RequestCode (strcat RequestCode (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
			)
			(setq Pos (+ Pos 1))
		)

		(setq LicenseKey (vl-registry-read (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "License Key"))
		(setq ListNumHash (vl-string->list RequestCode))
		(setq NumSeed (rem (apply '+ ListNumHash) NumSeedMax))
		(setq LicenseKeyExact "")
		(foreach NumHash ListNumHash
			(setq LicenseKeyExact (strcat LicenseKeyExact (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
		)
		(if (= LicenseKeyExact LicenseKey)
			(setq CheckLicense T)
			(progn
				(setq CheckLicense Nil)
				(setq LicenseKey "")
			)
		)
		(setq LicenseKeyExact Nil)

		(if (not CheckLicense)
			(LIC_LOAD_DIALOG NameSoftware)
		)

		(setq UserName (getenv "ComputerName"))
		(setq DateUsed (vl-registry-read (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "DateUsed"))
		(setq NumUsed (vl-registry-read (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "NumUsed"))
		(setq DateCurrent (LIC_GET_CURRENT_DATE))
		(if (not NumUsed)
			(setq NumUsed "0")
		)
		(if (not DateUsed)
			(setq DateUsed DateCurrent)
		)
		(setq NumUsed (itoa (+ (atoi NumUsed) 1)))
		(if (/= DateUsed DateCurrent)
			(progn
				(setq ListDataUser
					(list
						(cons "NameSoftware" NameSoftware)
						(cons "UserName" UserName)
						(cons "RequestCode" RequestCode)
						(cons "LicenseKey" LicenseKey)
						(cons "DateUsed" DateUsed)
						(cons "NumUsed" NumUsed)
					)
				)
				(if (LIC_SEND_REQUESTAPI_SENDDATAUSER ListDataUser)
					(progn
						(setq NumUsed "1")
						(setq DateUsed DateCurrent)
					)
				)
			)
		)
		(vl-registry-write (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "DateUsed" DateUsed)
		(vl-registry-write (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "NumUsed" NumUsed)
	)
	-------------------------------------------------------------------------------------------------------------------
	(LIC_REQUESTCODE)
	(setq LIC_REQUESTCODE nil)

	(vl-load-com)
	(setq VlaDrawingCurrent (vla-get-activedocument (vlax-get-acad-object)))
	(vla-startundomark VlaDrawingCurrent)
	(CAEX_SET_VARSYSTEM_CUE)
	(CAEX_CREATE_LISTVLALAYERLOCK)

	(vl-catch-all-apply (function (lambda ( / )
		(setq CheckActiveCell (CAEX_CHECKACTIVECELL))
		(if CheckActiveCell
			(progn
				(setq VlaLayersGroup (vla-get-layers VlaDrawingCurrent))
				(setq ToleranceValue 1e-8)
				(if (= (getvar "CVPORT") 1)
					(setq VlaSpace (vla-get-PaperSpace VlaDrawingCurrent))
					(setq VlaSpace (vla-get-ModelSpace VlaDrawingCurrent))
				)
				(setq SeparatorCSV (vl-registry-read "HKEY_CURRENT_USER\\Control Panel\\International" "sList"))
				(if (not SeparatorCSV)
					(setq SeparatorCSV ",")
				)

				(setq ListTemp (CAEX_SELECT_TABLE_IN_CAD))
				(setq ListVlaObjectLine (nth 0 ListTemp))
				(setq ListVlaObjectText (nth 1 ListTemp))
				(setq ListVlaObjectDelete (nth 2 ListTemp))

				(if ListVlaObjectLine
					(progn
						(setq ListTemp (CAEX_GET_LISTDATALINE_FROM_CAD))
						(setq ListDataLineX (nth 0 ListTemp))
						(setq ListDataLineY (nth 1 ListTemp))
						(setq ListCoordinateX (mapcar 'car ListDataLineX))
						(setq ListCoordinateY (mapcar 'car ListDataLineY))
						(setq GroupListAddress (CAEX_GET_GROUPLISTADDRESS_FROM_CAD))
						(setq ListTemp (CAEX_CREATE_LISTDATACELL))
						(setq ListDataCell (nth 0 ListTemp))
						(setq NameLayerCell (nth 1 ListTemp))
						(setq ListDataTable (CAEX_GET_LISTDATATABLE_FROM_CAD))
						(while T
							(setq GroupListAddressUpdate (CAEX_GET_GROUPLISTADDRESSUPDATE))
							(CAEX_UPDATE_TABLE_FOR_EXCEL)
						)
					)
				)
				
			)
			(princ "\nData from excel could not be found!")
		)
	)))

	(mapcar 'vla-delete (append ListVlaObjectDelete (mapcar 'car ListDataCell)))
	(if NameLayerCell
		(vla-delete (vla-item VlaLayersGroup NameLayerCell))
	)

	(CAEX_RESTORE_LOCK_LAYER)
	(CAEX_RESET_VARSYSTEM)
	(vla-endundomark VlaDrawingCurrent)
	(princ)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CREATE_LISTDATACELL ( / 
	DataPolyline
	ListBulge
	ListPoint
	ListDataCell
	NameLayerCell
	Point1
	Point2
	Point3
	Point4
	PointMinX
	PointMinY 
	PointMaxX 
	PointMaxY
	VlaPolylineCell)

	(setq NameLayerCell (CAEX_RANDOM_STRING 8))
	(while (not (vl-catch-all-error-p (vl-catch-all-apply 'vla-item (list VlaLayersGroup NameLayerCell))))
		(setq NameLayerCell (CAEX_RANDOM_STRING 8))
	)
	(vla-add VlaLayersGroup NameLayerCell)

	(foreach ListAddress GroupListAddress
		(setq PointMinX (nth (nth 0 ListAddress) ListCoordinateX))
		(setq PointMinY (nth (+ (nth 3 ListAddress) 1) ListCoordinateY))
		(setq PointMaxX (nth (+ (nth 2 ListAddress) 1) ListCoordinateX))
		(setq PointMaxY (nth (nth 1 ListAddress) ListCoordinateY))
		(setq Point1 (list PointMinX PointMinY 0.0))
		(setq Point2 (list PointMaxX PointMinY 0.0))
		(setq Point3 (list PointMaxX PointMaxY 0.0))
		(setq Point4 (list PointMinX PointMaxY 0.0))
		(setq ListPoint (list Point1 Point2 Point3 Point4))
		(setq ListBulge (mapcar '(lambda (x) 0.0) ListPoint))
		(setq DataPolyline (list ListPoint ListBulge))
		(setq VlaPolylineCell (CAEX_CREATE_POLYLINE DataPolyline T))
		(vla-put-layer VlaPolylineCell NameLayerCell)
		(vla-put-color VlaPolylineCell 256)
		(vla-put-Linetype VlaPolylineCell "ByLayer")
		(setq ListDataCell (cons (list VlaPolylineCell ListAddress) ListDataCell))
	)
	(list ListDataCell NameLayerCell)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_GROUPLISTADDRESSUPDATE ( / 
	GroupListAddressUpdate
	ListAddress
	ListVlaPolylineCell
	SelectionFilter
	SelectionSet)

	(setq SelectionFilter (list (cons 8 NameLayerCell)))
	(setq SelectionSet (ssget SelectionFilter))
	(setq ListVlaPolylineCell (CAEX_CONVERT_SELECTIONSET_TO_LISTVLAOBJECT SelectionSet))
	(foreach VlaPolylineCell ListVlaPolylineCell
		(vla-Highlight VlaPolylineCell 1)
		(setq ListAddress (nth 1 (assoc VlaPolylineCell ListDataCell)))
		(setq GroupListAddressUpdate (cons ListAddress GroupListAddressUpdate))
	)
	GroupListAddressUpdate
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_UPDATE_TABLE_FOR_EXCEL ( / 
	Alignment
	DataTable
	FontBold
	ListAddress
	ListAlignment
	FontName
	FontItalic
	NumColStart
	NumRowStart
	Rotation
	StringAddress
	StringContent
	VlaAppExcel
	VlaBorder
	VlaBorders
	VlaFont
	VlaRange
	VlaRangeActive
	VlaSheet)

	(setq VlaAppExcel (vlax-get-or-create-Object "Excel.Application"))
	(vlax-put-property VlaAppExcel "Visible" :vlax-true)
	(setq VlaSheet (vlax-get-property VlaAppExcel "ActiveSheet"))
	(setq VlaRangeActive (vlax-get-property VlaAppExcel "Selection"))
	(setq StringAddress (vlax-get-property VlaRangeActive "Address" :vlax-false :vlax-false 1 :vlax-false :vlax-false))
	(setq ListAddress (CAEX_STRINGADDRESS_TO_LISTADDRESS StringAddress))
	(setq NumColStart (nth 0 ListAddress))
	(setq NumRowStart (nth 1 ListAddress))

	(foreach ListAddressCad GroupListAddressUpdate
		(setq ListAddress (list (nth 0 ListAddressCad) (nth 1 ListAddressCad)))
		(setq DataTable (assoc (list 0 ListAddress) ListDataTable))
		(setq StringContent (nth 1 (assoc 2 DataTable)))
		(setq FontName (nth 1 (assoc 3 DataTable)))
		(setq FontBold (nth 1 (assoc 4 DataTable)))
		(setq FontItalic (nth 1 (assoc 5 DataTable)))
		(setq Alignment (nth 1 (assoc 7 DataTable)))
		(setq Rotation (nth 1 (assoc 8 DataTable)))

		(setq ListAddress (list (+ NumColStart (nth 0 ListAddressCad)) (+ NumRowStart (nth 1 ListAddressCad)) (+ NumColStart (nth 2 ListAddressCad)) (+ NumRowStart (nth 3 ListAddressCad))))
		(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
		(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
		(vlax-put-property VlaRange "MergeCells" 0)
		(vlax-put VlaRange "Value" "")
		(setq VlaBorders (vlax-get-property VlaRange "Borders"))
		(foreach BordersIndex (list 7 8 9 10 11 12)
			(setq VlaBorder (vlax-get-property VlaBorders "Item" BordersIndex))
			(vlax-put-property VlaBorder "LineStyle" 1)
			(vlax-put-property VlaBorder "Weight" 2)
		)

		(setq ListAddress (list (+ NumColStart (nth 0 ListAddressCad)) (+ NumRowStart (nth 1 ListAddressCad))))
		(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
		(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
		(vlax-put VlaRange "Formula" StringContent)
		(vlax-invoke-method VlaRange "Copy")
		(vlax-invoke-method VlaRange "PasteSpecial" -4163 -4142 :vlax-false :vlax-false)
		(vlax-put-property VlaAppExcel "CutCopyMode" :vlax-false)

		(setq ListAddress (list (+ NumColStart (nth 0 ListAddressCad)) (+ NumRowStart (nth 1 ListAddressCad)) (+ NumColStart (nth 2 ListAddressCad)) (+ NumRowStart (nth 3 ListAddressCad))))
		(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
		(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
		(if (= Rotation 0.0)
			(progn
				(setq ListAlignment
					(list
						(cons "Center" -4108)
						(cons "Left" -4131)
						(cons "Right" -4152)
					)
				)
				(setq Alignment (cdr (assoc Alignment ListAlignment)))
				(vlax-put-property VlaRange "HorizontalAlignment" Alignment)
			)
			(progn
				(setq ListAlignment
					(list
						(cons "Top" -4160)
						(cons "Center" -4108)
						(cons "Bottom" -4107)
					)
				)
				(setq Alignment (cdr (assoc Alignment ListAlignment)))
				(vlax-put-property VlaRange "VerticalAlignment" Alignment)
			)
		)
		(setq VlaFont (vlax-get-property VlaRange "Font"))
		(vlax-put-property VlaFont "Name" FontName)
		(vlax-put-property VlaFont "Bold" FontBold)
		(vlax-put-property VlaFont "Italic" FontItalic)
		(vlax-put-property VlaRange "Orientation" Rotation)
		(vlax-put-property VlaRange "MergeCells" 1)
		(vlax-put-property VlaRange "WrapText" 1)
	)
	(vlax-invoke-method VlaRangeActive "Select")
)
-------------------------------------------------------------------------------------------------------------------
(defun C:UPDATECADFROMEXCEL ( / 
	CheckActiveCell
	GroupListAddress
	GroupListAddressUpdate
	ListCoordinateX
	ListCoordinateY
	ListDataCell
	ListDataLineX
	ListDataLineY
	ListDataTable
	ListTemp
	ListVarSystem_OldValue
	ListVlaLayerLock
	ListVlaObjectLine
	ListVlaObjectDelete
	ListVlaObjectText
	NameLayerCell
	ToleranceValue
	VlaDrawingCurrent
	VlaLayersGroup
	VlaSpace)
	-------------------------------------------------------------------------------------------------------------------
	(defun LIC_REQUESTCODE ( / 
		CheckLicense
		CodeSoftware
		DateCurrent
		DateUsed
		ListDataUser
		ListCharTotal
		ListNumHash
		ListSerialNumTotal
		ListSerialString
		ListStringQuery
		NameSoftware
		NumCharTotal
		NumCodeSoftware
		NumRequestCode
		NumHash
		NumSeedMax
		NumSeed
		NumUsed
		Pos
		LicenseKey
		LicenseKeyExact
		RequestCode
		UserName)

		(setq NameSoftware "Cad Link Excel")
		(defun LIC_LOAD_DIALOG ( NameSoftware /
			End_Main_DCL
			Main_DCL
			LicenseKey)

			(defun LIC_GET_TILE_LICENSENUMBER ( / )
				(setq LicenseKey (vl-string-trim " " (get_tile "Tile_LicenseNumber")))
				(setq ListNumHash (vl-string->list RequestCode))
				(setq NumSeed (rem (apply '+ ListNumHash) NumSeedMax))
				(setq LicenseKeyExact "")
				(foreach NumHash ListNumHash
					(setq LicenseKeyExact (strcat LicenseKeyExact (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
				)
				(if (= LicenseKeyExact LicenseKey)
					(progn
						(vl-registry-write (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "License Key" LicenseKey)
						(set_tile "Tile_ActivateText" "The official version has been activated!")
						(mode_tile "Tile_ActivateText" 1)
						(mode_tile "Tile_LicenseNumber" 1)
					)
					(progn
						(set_tile "Tile_ActivateText" "License number is incorrect!")
						(mode_tile "Tile_ActivateText" 1)
						(mode_tile "Tile_LicenseNumber" 0)
					)
				)
				(setq LicenseKeyExact Nil)
			)

			(LIC_MAKE_FILE_DCL NameSoftware)
			(setq Main_DCL (load_dialog (strcat NameSoftware " License.dcl")))
			(new_dialog (strcat (LIC_REMOVE_SPACE_OF_STRING NameSoftware) "License") Main_DCL)

			(setq LicenseKey "")
			(LIC_SET_TILE_DECORATION 4)
			(set_tile "Tile_RequestCode" RequestCode)

			(action_tile "Tile_LicenseNumber" "(LIC_GET_TILE_LICENSENUMBER)")
			(action_tile "Tile_CopyRequestCode" "(vlax-invoke (vlax-get (vlax-get (vlax-create-object \"HTMLFile\") 'ParentWindow) 'ClipBoardData) 'setData \"Text\" RequestCode)")

			(setq End_Main_DCL (start_dialog))
			(cond
				(
					(or
						(= End_Main_DCL 0)
						(= End_Main_DCL 1)
					)
					(unload_dialog Main_DCL)
				)
			)
			(setq LIC_GET_TILE_LICENSENUMBER Nil)
			(princ)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_MAKE_FILE_DCL ( NameSoftware /
			Num
			DclFile
			DirectoryDes)

			(setq DirectoryDes (strcat (getvar "roamablerootprefix") "Support"))
			(setq DclFile (open (strcat DirectoryDes "\\" NameSoftware " License.dcl") "w"))
			(write-line "///------------------------------------------------------------------------" DclFile)
			(write-line (strcat "///		 " NameSoftware " License.dcl") DclFile)
			(write-line (strcat (LIC_REMOVE_SPACE_OF_STRING NameSoftware) "License:dialog{") DclFile)
			(write-line (strcat "label = \"" NameSoftware " License\";") DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"Tile_ActivateText\";" DclFile)
			(write-line "	alignment = centered;" DclFile)
			(write-line "	width = 60;" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep0\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "		:column{" DclFile)
			(write-line "		width = 60;" DclFile)

			(write-line "			:text{" DclFile)
			(write-line "				label = \"     You are using the trial version of this app.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     If you find it useful, you can pay 5 USD per license.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     Then you will not see this message board every time you use the application.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     <NAME_EMAIL> (+84 933 648 160) .\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     Thank you for using and supporting.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "		}" DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep1\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:row{" DclFile)
			(write-line "		:text{" DclFile)
			(write-line "		label = \"     Request code\";" DclFile)
			(write-line "		width = 15;" DclFile)
			(write-line "		}" DclFile)

			(write-line "		:text{" DclFile)
			(write-line "		key = \"Tile_RequestCode\";" DclFile)
			(write-line "		width = 45;" DclFile)
			(write-line "		}" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep2\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:row{" DclFile)
			(write-line "		:text{" DclFile)
			(write-line "		label = \"     Enter license\";" DclFile)
			(write-line "		width = 15;" DclFile)
			(write-line "		}" DclFile)

			(write-line "		:edit_box{" DclFile)
			(write-line "		key = \"Tile_LicenseNumber\";" DclFile)
			(write-line "		width = 45;" DclFile)
			(write-line "		}" DclFile)
			(write-line "	}" DclFile)  
		
			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep3\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:row{" DclFile)
			(write-line "		:button{" DclFile)
			(write-line "		label = \"&Continue\";" DclFile)
			(write-line "		key = \"Ok\";" DclFile)
			(write-line "		is_default = true;" DclFile)
			(write-line "		is_cancel = true;" DclFile)
			(write-line "		width = 30;" DclFile)
			(write-line "		}" DclFile)

			(write-line "		:button{" DclFile)
			(write-line "		label = \"Copy &request code\";" DclFile)
			(write-line "		key = \"Tile_CopyRequestCode\";" DclFile)
			(write-line "		width = 30;" DclFile)
			(write-line "		}" DclFile)
			(write-line "	}" DclFile)
			(write-line "}" DclFile)
			(close DclFile)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_SET_TILE_DECORATION ( NumTotal / Num Tile)
			(setq Num 0)
			(repeat NumTotal
				(setq Tile (strcat "sep" (itoa Num)))
				(set_tile Tile "--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------")
				(setq Num (+ Num 1))
			)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_REMOVE_SPACE_OF_STRING ( String / )
			(while (/= String (setq StringTemp (vl-string-subst "" " " String)))
				(setq String StringTemp)
			)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_GETSERIALNUMBER ( StringQuery StringNameSerial / 
			SerialNumber
			VlaObjectLocal
			VlaObjectServive
			VlaObjectSet)

			(setq VlaObjectLocal (vlax-create-object "WbemScripting.SWbemLocator"))
			(setq VlaObjectServive (vlax-invoke VlaObjectLocal 'ConnectServer nil nil nil nil nil nil nil nil))
			(setq Server (vlax-invoke VlaObjectLocal 'ConnectServer "." "root\\cimv2"))
			(setq VlaObjectSet
				(vlax-invoke 
					VlaObjectServive
					"ExecQuery"
					StringQuery
				)
			)
			(vlax-for VlaObject VlaObjectSet
				(setq SerialNumber (vlax-get VlaObject StringNameSerial))
			)
			SerialNumber
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_SEND_REQUESTAPI ( StringUrl / 
			CodeStatus
			StringResponse
			VlaHttpRequest)

			(vl-catch-all-apply (function (lambda ( / )
				(setq VlaHttpRequest (vlax-invoke-method (vlax-get-acad-object) 'GetInterfaceObject "WinHttp.WinHttpRequest.5.1"))
				(vlax-invoke-method VlaHttpRequest 'Open "GET" StringUrl :vlax-false)
				(vlax-invoke-method VlaHttpRequest 'Send)
				(setq CodeStatus (vlax-get-property VlaHttpRequest 'Status))
				(if (= CodeStatus 200)
					(setq StringResponse (vlax-get-property VlaHttpRequest 'ResponseText))
				)
			)))
			StringResponse
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_SEND_REQUESTAPI_SENDDATAUSER ( ListData / 
			StringResponse
			StringUrl)

			(vl-catch-all-apply (function (lambda ( / )
				(setq StringUrl "https://script.google.com/macros/s/AKfycbz1n3v780vBayl68zGEGeEc0swT9HgO1Sg5c_bz4xK-tO9oUARczwj59oH4UCPcgify/exec?")
				(foreach Data ListData
					(setq StringUrl (strcat StringUrl (car Data) "=" (cdr Data) "&"))
				)
				(setq StringResponse (LIC_SEND_REQUESTAPI StringUrl))
			)))
			StringResponse
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_GET_CURRENT_DATE ( / )
			(setq StringTemp (rtos (getvar "CDATE") 2 6))
			(strcat (substr StringTemp 1 4) "-" (substr StringTemp 5 2) "-" (substr StringTemp 7 2))
		)
		-------------------------------------------------------------------------------------------------------------------
		(vl-load-com)
		(setq ListStringQuery
			(list
				(cons "Select * from Win32_BaseBoard" "SerialNumber")
				(cons "Select * from Win32_BIOS" "SerialNumber")
			)
		)
		(setq ListSerialString (cons NameSoftware (mapcar '(lambda (x) (LIC_GETSERIALNUMBER (car x) (cdr x))) ListStringQuery)))
		(setq ListSerialString (vl-remove Nil ListSerialString))
		(setq ListSerialNumTotal (mapcar 'vl-string->list ListSerialString))
		(setq LIC_GETSERIALNUMBER Nil)

		(setq ListCharTotal (list "A" "B" "C" "D" "E" "F" "G" "H" "I" "J" "K" "L" "M" "N" "O" "P" "Q" "R" "S" "T" "U" "V" "W" "X" "Y" "Z" "0" "1" "2" "3" "4" "5" "6" "7" "8" "9"))
		(setq NumCharTotal (length ListCharTotal))
		(setq NumSeedMax 100000000)

		(setq ListNumHash (vl-string->list NameSoftware))
		(setq NumSeed (rem (apply '+ ListNumHash) NumSeedMax))
		(setq CodeSoftware "")
		(setq NumCodeSoftware 6)
		(setq Pos 0)
		(while (< (strlen CodeSoftware) NumCodeSoftware)
			(setq NumHash (nth 0 ListNumHash))
			(if (not NumHash)
				(setq NumHash NumSeed)
			)
			(setq CodeSoftware (strcat CodeSoftware (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
		)

		(setq RequestCode CodeSoftware)
		(setq Pos 0)
		(setq NumRequestCode 36)
		(while (< (strlen RequestCode) NumRequestCode)
			(foreach ListSerialNum ListSerialNumTotal
				(setq NumHash Nil)
				(vl-catch-all-apply (function (lambda ( / )
					(setq NumHash (nth Pos ListSerialNum))
				)))
				(if (not NumHash)
					(setq NumHash NumSeed)
				)
				(setq RequestCode (strcat RequestCode (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
			)
			(setq Pos (+ Pos 1))
		)

		(setq LicenseKey (vl-registry-read (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "License Key"))
		(setq ListNumHash (vl-string->list RequestCode))
		(setq NumSeed (rem (apply '+ ListNumHash) NumSeedMax))
		(setq LicenseKeyExact "")
		(foreach NumHash ListNumHash
			(setq LicenseKeyExact (strcat LicenseKeyExact (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
		)
		(if (= LicenseKeyExact LicenseKey)
			(setq CheckLicense T)
			(progn
				(setq CheckLicense Nil)
				(setq LicenseKey "")
			)
		)
		(setq LicenseKeyExact Nil)

		(if (not CheckLicense)
			(LIC_LOAD_DIALOG NameSoftware)
		)

		(setq UserName (getenv "ComputerName"))
		(setq DateUsed (vl-registry-read (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "DateUsed"))
		(setq NumUsed (vl-registry-read (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "NumUsed"))
		(setq DateCurrent (LIC_GET_CURRENT_DATE))
		(if (not NumUsed)
			(setq NumUsed "0")
		)
		(if (not DateUsed)
			(setq DateUsed DateCurrent)
		)
		(setq NumUsed (itoa (+ (atoi NumUsed) 1)))
		(if (/= DateUsed DateCurrent)
			(progn
				(setq ListDataUser
					(list
						(cons "NameSoftware" NameSoftware)
						(cons "UserName" UserName)
						(cons "RequestCode" RequestCode)
						(cons "LicenseKey" LicenseKey)
						(cons "DateUsed" DateUsed)
						(cons "NumUsed" NumUsed)
					)
				)
				(if (LIC_SEND_REQUESTAPI_SENDDATAUSER ListDataUser)
					(progn
						(setq NumUsed "1")
						(setq DateUsed DateCurrent)
					)
				)
			)
		)
		(vl-registry-write (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "DateUsed" DateUsed)
		(vl-registry-write (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "NumUsed" NumUsed)
	)
	-------------------------------------------------------------------------------------------------------------------
	(LIC_REQUESTCODE)
	(setq LIC_REQUESTCODE nil)

	(vl-load-com)
	(setq VlaDrawingCurrent (vla-get-activedocument (vlax-get-acad-object)))
	(vla-startundomark VlaDrawingCurrent)
	(CAEX_SET_VARSYSTEM_EUC)
	(CAEX_CREATE_LISTVLALAYERLOCK)

	(vl-catch-all-apply (function (lambda ( / )
		(setq CheckActiveCell (CAEX_CHECKACTIVECELL))
		(if CheckActiveCell
			(progn
				(setq VlaLayersGroup (vla-get-layers VlaDrawingCurrent))
				(setq ToleranceValue 1e-8)
				(if (= (getvar "CVPORT") 1)
					(setq VlaSpace (vla-get-PaperSpace VlaDrawingCurrent))
					(setq VlaSpace (vla-get-ModelSpace VlaDrawingCurrent))
				)

				(setq ListTemp (CAEX_SELECT_TABLE_IN_CAD))
				(setq ListVlaObjectLine (nth 0 ListTemp))
				(setq ListVlaObjectText (nth 1 ListTemp))
				(setq ListVlaObjectDelete (nth 2 ListTemp))

				(if ListVlaObjectLine
					(progn
						(setq ListTemp (CAEX_GET_LISTDATALINE_FROM_CAD))
						(setq ListDataLineX (nth 0 ListTemp))
						(setq ListDataLineY (nth 1 ListTemp))
						(setq ListCoordinateX (mapcar 'car ListDataLineX))
						(setq ListCoordinateY (mapcar 'car ListDataLineY))
						(setq NumColTotal (- (length ListCoordinateX) 1))
						(setq NumRowTotal (- (length ListCoordinateY) 1))
						(setq GroupListAddress (CAEX_GET_GROUPLISTADDRESS_FROM_CAD))
						(setq ListTemp (CAEX_CREATE_LISTDATACELL))
						(setq ListDataCell (nth 0 ListTemp))
						(setq NameLayerCell (nth 1 ListTemp))
						(setq HeightTextGlobal (CAEX_GET_HEIGHTTEXTGLOBAL ListVlaObjectText))
						(setq ListPropertyObjectGlobal (CAEX_GET_LISTPROPERTYOBJECT_MULTIOBJECT ListVlaObjectText))

						(setq ListTemp (CAEX_GET_LISTDATATABLE_FROM_EXCEL NumColTotal NumRowTotal))
						(setq ListDataTable (nth 0 ListTemp))
						(while T
							(setq GroupListAddressUpdate (CAEX_GET_GROUPLISTADDRESSUPDATE))
							(CAEX_UPDATE_TABLE_FOR_CAD)
						)
					)
				)
				
			)
			(princ "\nData from excel could not be found!")
		)
	)))

	(mapcar 'vla-delete (append ListVlaObjectDelete (mapcar 'car ListDataCell)))
	(if NameLayerCell
		(vla-delete (vla-item VlaLayersGroup NameLayerCell))
	)

	(CAEX_RESTORE_LOCK_LAYER)
	(CAEX_RESET_VARSYSTEM)
	(vla-endundomark VlaDrawingCurrent)
	(princ)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_HEIGHTTEXTGLOBAL ( ListVlaObjectText /
	HeightText
	HeightTextGlobal
	ListHeightText
	Num)

	(setq HeightTextGlobal (CAEX_FIND_VALUE_POPULAR (mapcar 'vla-get-Height ListVlaObjectText)))
	(if (not HeightTextGlobal)
		(progn
			(setq Num 0)
			(repeat (- (length ListCoordinateY) 1)
				(setq HeightText (/ (- (nth Num ListCoordinateY) (nth (+ Num 1) ListCoordinateY)) 2.0))
				(setq ListHeightText (cons HeightText ListHeightText))
				(setq Num (+ Num 1))
			)
			(setq HeightTextGlobal (CAEX_FIND_VALUE_POPULAR ListHeightText))
		)
	)
	HeightTextGlobal
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTPROPERTYOBJECT_MULTIOBJECT ( ListVlaObjectText / 
	GroupListPropertyObject
	ListPropertyObjectGlobal
	ListNameProperty)

	(setq GroupListPropertyObject (mapcar 'CAEX_GET_PROPERTIES_OBJECT ListVlaObjectText))
	(setq ListNameProperty (mapcar 'car (car GroupListPropertyObject)))
	(setq ListPropertyObjectGlobal
		(mapcar
			'(lambda (NameProperty)
				(CAEX_FIND_VALUE_POPULAR
					(mapcar '(lambda (ListPropertyObject) (assoc NameProperty ListPropertyObject)) GroupListPropertyObject)
				)
			)
			ListNameProperty
		)
	)
	ListPropertyObjectGlobal
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_UPDATE_TABLE_FOR_CAD ( /
	Alignment
	CodeAlignment
	DataTable
	DeltaX
	DeltaY
	FontBold
	FontItalic
	FontName
	GapText
	HeightText
	LengthMaxX
	LengthMaxY
	LengthTextX
	LengthTextY
	ListDataBoundary
	ListPropertyObject
	ListStringContent
	ListVlaObjectTextSelect
	ListVlaObjectTextNew
	NameTextStyle
	NumText
	PointMaxX
	PointMaxY
	PointMinX
	PointMinY
	PointText
	PointTextBase
	PointTextTemp
	PointTextX
	PointTextY
	Rotation
	SelectionFilter
	ValueScaleText
	VlaObjectText)

	(setq SelectionFilter
		(list
			(cons -4 "<OR")
			(cons 0 "TEXT")
			(cons 0 "MTEXT")
			(cons -4 "OR>")
		)
	)

	(foreach ListAddress GroupListAddressUpdate
		(setq DataTable (assoc (list 1 ListAddress) ListDataTable))
		(if DataTable
			(progn
				(setq ListStringContent (nth 1 (assoc 2 DataTable)))
				(setq FontName (nth 1 (assoc 3 DataTable)))
				(setq FontBold (nth 1 (assoc 4 DataTable)))
				(setq FontItalic (nth 1 (assoc 5 DataTable)))
				(setq Alignment (nth 1 (assoc 7 DataTable)))
				(setq Rotation (nth 1 (assoc 8 DataTable)))
				(setq ListVlaObjectTextSelect (CAEX_GET_LISTVLATEXT_IN_CELL ListAddress))
				(if ListStringContent
					(progn
						(setq HeightText (CAEX_FIND_VALUE_POPULAR (mapcar 'vla-get-Height ListVlaObjectTextSelect)))
						(if (not HeightText)
							(setq HeightText HeightTextGlobal)
						)
						(setq ListPropertyObject (CAEX_GET_LISTPROPERTYOBJECT_MULTIOBJECT ListVlaObjectTextSelect))
						(if (not ListPropertyObject)
							(setq ListPropertyObject ListPropertyObjectGlobal)
						)

						(setq PointMinX (nth (nth 0 ListAddress) ListCoordinateX))
						(setq PointMaxY (nth (nth 1 ListAddress) ListCoordinateY))
						(setq PointMaxX (nth (+ (nth 2 ListAddress) 1) ListCoordinateX))
						(setq PointMinY (nth (+ (nth 3 ListAddress) 1) ListCoordinateY))

						(setq NameTextStyle (CAEX_FIND_NAMETEXTSTYLE FontName FontBold FontItalic))
						(setq ListVlaObjectTextNew Nil)
						(setq NumText (length ListStringContent))

						(if (= Rotation 0.0)
							(progn
								(setq GapText (/ (- PointMaxY PointMinY (* NumText HeightText)) (+ NumText 1)))
								(cond
									((= Alignment "Left")
										(setq PointTextX (+ PointMinX (* HeightText 0.5)))
										(setq CodeAlignment acAlignmentBottomLeft)
									)
									((= Alignment "Center")
										(setq PointTextX (+ PointMinX (* (- PointMaxX PointMinX) 0.5)))
										(setq CodeAlignment acAlignmentBottomCenter)
									)
									((= Alignment "Right")
										(setq PointTextX (- PointMaxX (* HeightText 0.5)))
										(setq CodeAlignment acAlignmentBottomRight)
									)
								)
								(setq PointTextY (- PointMaxY GapText (* HeightText 1.0)))
								(setq PointText (list PointTextX PointTextY 0.0))
								(foreach StringContent ListStringContent
									(setq VlaObjectText (vla-AddText VlaSpace StringContent (vlax-3d-point PointText) HeightText))
									(setq ListVlaObjectTextNew (cons VlaObjectText ListVlaObjectTextNew))
									(setq ListVlaObjectOfTable (cons VlaObjectText ListVlaObjectOfTable))
									(vla-put-alignment VlaObjectText CodeAlignment)
									(setq PointTextTemp
										(list
											(nth 0 PointText)
											(- (nth 1 PointText) (nth 1 (vlax-safearray->list (vlax-variant-value (vla-get-InsertionPoint VlaObjectText)))))
											(nth 2 PointText)
										)
									)
									(vla-put-TextAlignmentPoint VlaObjectText (vlax-3d-point PointTextTemp))
									(vla-put-StyleName VlaObjectText NameTextStyle)
									(setq PointTextY (- PointTextY GapText HeightText))
									(setq PointText (list PointTextX PointTextY 0.0))
								)
							)
							(progn
								(setq GapText (/ (- PointMaxX PointMinX (* NumText HeightText)) (+ NumText 1)))
								(cond
									((= Alignment "Bottom")
										(progn
											(setq PointTextY (+ PointMinY (* HeightText 0.5)))
											(if (= Rotation (* Pi 0.5))
												(setq CodeAlignment acAlignmentBottomLeft)
												(setq CodeAlignment acAlignmentBottomRight)
											)
										)
									)
									((= Alignment "Center")
										(setq PointTextY (+ PointMinY (* (- PointMaxY PointMinY) 0.5)))
										(setq CodeAlignment acAlignmentBottomCenter)
									)
									((= Alignment "Top")
										(progn
											(setq PointTextY (- PointMaxY (* HeightText 0.5)))
											(if (= Rotation (* Pi 0.5))
												(setq CodeAlignment acAlignmentBottomRight)
												(setq CodeAlignment acAlignmentBottomLeft)
											)
										)
									)
								)
								(if (= Rotation (* Pi 0.5))
									(progn
										(setq PointTextX (+ PointMinX GapText (* HeightText 1.0)))
									)
									(progn
										(setq ListStringContent (reverse ListStringContent))
										(setq PointTextX (+ PointMinX GapText (* HeightText 0.0)))
									)
								)
								(setq PointText (list PointTextX PointTextY 0.0))
								(foreach StringContent ListStringContent
									(setq VlaObjectText (vla-AddText VlaSpace StringContent (vlax-3d-point PointText) HeightText))
									(setq ListVlaObjectTextNew (cons VlaObjectText ListVlaObjectTextNew))
									(setq ListVlaObjectOfTable (cons VlaObjectText ListVlaObjectOfTable))
									(vla-put-Rotation VlaObjectText Rotation)
									(vla-put-alignment VlaObjectText CodeAlignment)
									(setq PointTextTemp
										(list
											(- (nth 0 PointText) (nth 0 (vlax-safearray->list (vlax-variant-value (vla-get-InsertionPoint VlaObjectText)))))
											(nth 1 PointText)
											(nth 2 PointText)
										)
									)
									(vla-put-TextAlignmentPoint VlaObjectText (vlax-3d-point PointTextTemp))
									(vla-put-StyleName VlaObjectText NameTextStyle)
									(setq PointTextX (+ PointTextX GapText HeightText))
									(setq PointText (list PointTextX PointTextY 0.0))
								)
							)
						)

						(setq ListDataBoundary (mapcar 'CAEX_GET_POINTMIN_POINTMAX_TEXT_MTEXT ListVlaObjectTextNew))
						(setq ListDataBoundary (vl-remove ListDataBoundary Nil))
						(if ListDataBoundary
							(progn
								(setq LengthTextX
									(-
										(apply 'max (mapcar '(lambda (x) (nth 0 (nth 1 x))) ListDataBoundary))
										(apply 'min (mapcar '(lambda (x) (nth 0 (nth 0 x))) ListDataBoundary))
									)
								)
								(setq LengthTextY
									(-
										(apply 'max (mapcar '(lambda (x) (nth 1 (nth 1 x))) ListDataBoundary))
										(apply 'min (mapcar '(lambda (x) (nth 1 (nth 0 x))) ListDataBoundary))
									)
								)
							)
							(progn
								(setq LengthTextX 0.0)
								(setq LengthTextY 0.0)
							)
						)
						(setq LengthMaxX (- PointMaxX PointMinX (* HeightText 0.1)))
						(setq DeltaX (- LengthMaxX LengthTextX))
						(setq LengthMaxY (- PointMaxY PointMinY (* HeightText 0.1)))
						(setq DeltaY (- LengthMaxY LengthTextY))
						(if
							(or
								(< DeltaX 0.0)
								(< DeltaY 0.0)
							)
							(progn
								(if (< DeltaX DeltaY)
									(progn
										(setq LengthMax LengthMaxX)
										(setq LengthText LengthTextX)
									)
									(progn
										(setq LengthMax LengthMaxY)
										(setq LengthText LengthTextY)
									)
								)
								(setq ValueScaleText (/ LengthMax LengthText))

								(if (= Rotation 0.0)
									(progn
										(cond
											((= Alignment "Left")
												(setq PointTextBase
													(list
														PointMinX
														(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
														0.0
													)
												)
											)
											((= Alignment "Center")
												(setq PointTextBase
													(list
														(+ PointMinX (* (- PointMaxX PointMinX) 0.5))
														(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
														0.0
													)
												)
											)
											((= Alignment "Right")
												(setq PointTextBase
													(list
														PointMaxX
														(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
														0.0
													)
												)
											)
										)
									)
									(progn
										(cond
											((= Alignment "Left")
												(setq PointTextBase
													(list
														PointMinX
														(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
														0.0
													)
												)
											)
											((= Alignment "Center")
												(setq PointTextBase
													(list
														(+ PointMinX (* (- PointMaxX PointMinX) 0.5))
														(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
														0.0
													)
												)
											)
											((= Alignment "Right")
												(setq PointTextBase
													(list
														PointMaxX
														(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
														0.0
													)
												)
											)
										)
									)
								)

								(foreach VlaObjectText ListVlaObjectTextNew
									(vla-ScaleEntity VlaObjectText (vlax-3d-point PointTextBase) ValueScaleText)
								)
							)
						)

						(foreach VlaObjectText ListVlaObjectTextNew
							(CAEX_PUT_PROPERTIES_OBJECT VlaObjectText ListPropertyObject)
						)
					)
				)
				(foreach VlaObjectText ListVlaObjectTextSelect
					(setq ListVlaObjectText (vl-remove VlaObjectText ListVlaObjectText))
				)
				(setq ListVlaObjectText (append ListVlaObjectText ListVlaObjectTextNew))
				(mapcar 'vla-delete ListVlaObjectTextSelect)
			)
			(princ "\nCan't find matching value on excel!")
		)
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_LISTADDRESS_TO_STRINGADDRESS ( ListAddress /
	NumLength
	StringAddress)

	(setq NumLength (length ListAddress))
	(if (= NumLength 2)
		(setq StringAddress
			(strcat
				(CAEX_NUMCOLUMN_TO_STRINGCOLUMN (nth 0 ListAddress))
				(itoa (+ (nth 1 ListAddress) 1))
			)
		)
	)
	(if (= NumLength 4)
		(setq StringAddress
			(strcat
				(CAEX_NUMCOLUMN_TO_STRINGCOLUMN (nth 0 ListAddress))
				(itoa (+ (nth 1 ListAddress) 1))
				":"
				(CAEX_NUMCOLUMN_TO_STRINGCOLUMN (nth 2 ListAddress))
				(itoa (+ (nth 3 ListAddress) 1))
			)
		)
	)
	StringAddress
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_STRINGADDRESS_TO_LISTADDRESS ( StringAddress / 
	ListStringAddressCell
	ListAddress)

	(setq ListStringAddressCell (CAEX_STRING_TO_LIST_NEW StringAddress ":"))
	(setq ListAddress (apply 'append (mapcar 'CAEX_STRINGADDRESSCELL_TO_LISTADDRESSCELL ListStringAddressCell)))
	ListAddress
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_STRINGADDRESSCELL_TO_LISTADDRESSCELL ( StringAddressCell / 
	Char
	ListAddressCell
	Pos
	PosBase
	PosTotal)

	(setq PosTotal (strlen StringAddressCell))
	(setq Pos 1)
	(while
		(and
			(not PosBase)
			(<= Pos PosTotal)
		)
		(setq Char (substr StringAddressCell Pos 1))
		(if (member Char (list "0" "1" "2" "3" "4" "5" "6" "7" "8" "9"))
			(setq PosBase Pos)
		)
		(setq Pos (+ Pos 1))
	)
	(setq ListAddressCell
		(list
			(CAEX_STRINGCOLUMN_TO_NUMCOLUMN (substr StringAddressCell 1 (- PosBase 1)))
			(- (atoi (substr StringAddressCell PosBase)) 1)
		)
	)
	ListAddressCell
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_STRINGCOLUMN_TO_NUMCOLUMN ( StringColumn / 
	Base
	NumColumn
	ListTemp)

	(setq Base 1)
	(setq ListTemp (reverse (mapcar '(lambda (x) (- x 64)) (vl-string->list StringColumn))))
	(setq NumColumn 0)
	(foreach Temp ListTemp
		(setq NumColumn (+ NumColumn (* Temp Base)))
		(setq Base (* Base 26))
	)
	(setq NumColumn (- NumColumn 1))
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_NUMCOLUMN_TO_STRINGCOLUMN ( NumColumn /
	NumMod
	StringColumn)

	(setq NumColumn (+ NumColumn 1))
	(setq StringColumn "")
	(while (> NumColumn 0)
		(setq NumMod (rem NumColumn 26))
		(setq StringColumn
			(strcat
				(if (= NumMod 0) "Z" (chr (+ NumMod 64)))
				StringColumn
			)
		)
		(if (= NumMod 0)
			(setq NumColumn (- (/ NumColumn 26) 1))
			(setq NumColumn (/ NumColumn 26))
		)
	)
	StringColumn
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_STRINGCONTENT_MULTIOBJECT ( ListVlaObject / 
	ListDataText
	ListPointMax
	ListPointMin
	ListStringContent
	ListTemp
	NumRoundPosition
	PointMax
	PointMin
	PointMinX
	PointMinY
	StringContent
	StringTemp
	Temp1
	Temp2)

	(setq NumRoundPosition (* (apply 'min (mapcar 'vla-get-height ListVlaObject)) 0.25))
	(foreach VlaObject ListVlaObject
		(setq ListTemp (CAEX_GET_POINTMIN_POINTMAX_TEXT_MTEXT VlaObject))
		(setq PointMin (nth 0 ListTemp))
		(setq PointMax (nth 1 ListTemp))
		(setq ListPointMin (cons PointMin ListPointMin))
		(setq ListPointMax (cons PointMax ListPointMax))

		(setq PointMin (mapcar '(lambda (x) (CAEX_ROUNDOFF_NUMBER x NumRoundPosition)) PointMin))
		(setq PointMax (mapcar '(lambda (x) (CAEX_ROUNDOFF_NUMBER x NumRoundPosition)) PointMax))
		(setq PointMinX (nth 0 PointMin))
		(setq PointMinY (nth 1 PointMin))
		(setq StringTemp (CAEX_GET_LISTSTRINGCONTENT VlaObject))
		(setq Temp2 (cons StringTemp PointMinX))
		(if (setq Temp1 (assoc PointMinY ListDataText))
			(if (not (member Temp2 Temp1))
				(setq ListDataText (subst (append Temp1 (list Temp2)) Temp1 ListDataText))
			)
			(setq ListDataText (cons (list PointMinY Temp2) ListDataText))
		)
	)

	(setq ListDataText (vl-sort ListDataText '(lambda (a b) (> (car a) (car b)))))
	(setq ListDataText
		(mapcar
			'(lambda (x) 
				(vl-sort (cdr x) '(lambda (a b) (< (cdr a) (cdr b))))
			)
			ListDataText
		)
	)

	(setq ListStringContent (mapcar '(lambda (x) (CAEX_LIST_TO_STRING (mapcar 'car (mapcar 'car x)) " ")) ListDataText))
	(setq StringContent (CAEX_LISTSTRING_TO_FORMULAR_EXCEL ListStringContent))
	(setq PointMin
		(list
			(apply 'min (mapcar '(lambda (x) (nth 0 x)) ListPointMin))
			(apply 'min (mapcar '(lambda (x) (nth 1 x)) ListPointMin))
			0.0
		)
	)
	(setq PointMax
		(list
			(apply 'max (mapcar '(lambda (x) (nth 0 x)) ListPointMax))
			(apply 'max (mapcar '(lambda (x) (nth 1 x)) ListPointMax))
			0.0
		)
	)
	(list StringContent PointMin PointMax)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_POINTMIN_POINTMAX_TEXT_MTEXT ( VlaObject /
	ListPointMax
	ListPointMin
	ListVlaObjectTemp
	PointMax
	PointMin
	SelectionSetTemp
	TypeObject
	VlaObjectCopy)

	(setq TypeObject (vla-get-ObjectName VlaObject))
	(if (= TypeObject "AcDbText")
		(progn
			(vl-catch-all-apply (function (lambda ( / )
				(vla-GetBoundingBox VlaObject 'PointMin 'PointMax)
				(setq PointMin (vlax-safearray->list PointMin))
				(setq PointMax (vlax-safearray->list PointMax))
			)))
		)
	)

	(if (= TypeObject "AcDbMText")
		(progn
			; Kiem tra acet-explode co san khong
			(if (boundp 'acet-explode)
				(progn
					(setq VlaObjectCopy (vla-copy VlaObject))
					(setq SelectionSetTemp (acet-explode (vlax-vla-object->ename VlaObjectCopy)))
					(setq ListVlaObjectTemp (CAEX_CONVERT_SELECTIONSET_TO_LISTVLAOBJECT SelectionSetTemp))
					(foreach VlaObjectTemp ListVlaObjectTemp
						(vl-catch-all-apply (function (lambda ( / )
							(vla-GetBoundingBox VlaObjectTemp 'PointMin 'PointMax)
							(setq PointMin (vlax-safearray->list PointMin))
							(setq PointMax (vlax-safearray->list PointMax))
							(setq ListPointMin (cons PointMin ListPointMin))
							(setq ListPointMax (cons PointMax ListPointMax))
						)))
					)
					(if ListPointMin
						(setq PointMin
							(list
								(apply 'min (mapcar '(lambda (x) (nth 0 x)) ListPointMin))
								(apply 'min (mapcar '(lambda (x) (nth 1 x)) ListPointMin))
								0.0
							)
						)
						(setq PointMin Nil)
					)
					(if ListPointMax
						(setq PointMax
							(list
								(apply 'max (mapcar '(lambda (x) (nth 0 x)) ListPointMax))
								(apply 'max (mapcar '(lambda (x) (nth 1 x)) ListPointMax))
								0.0
							)
						)
						(setq PointMax Nil)
					)
					(mapcar 'vla-delete ListVlaObjectTemp)
				)
				(progn
					; Fallback: su dung GetBoundingBox truc tiep cho MTEXT
					(vl-catch-all-apply (function (lambda ( / )
						(vla-GetBoundingBox VlaObject 'PointMin 'PointMax)
						(setq PointMin (vlax-safearray->list PointMin))
						(setq PointMax (vlax-safearray->list PointMax))
					)))
					(princ "\nWarning: acet-explode not available, using direct MTEXT bounding box")
				)
			)
		)
	)

	(if (and PointMin PointMax)
		(list PointMin PointMax)
		Nil
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTSTRINGCONTENT ( VlaObject /
	ListStringContent
	ListVlaObjectTemp
	SelectionSetTemp
	StringTemp
	TypeObject
	VlaObjectCopy)

	(setq TypeObject (vla-get-ObjectName VlaObject))
	(if (= TypeObject "AcDbText")
		(progn
			(setq StringTemp (cdr (assoc 1 (entget (vlax-vla-object->ename VlaObject)))))
			(setq ListStringContent (list StringTemp))
		)
	)
	(if (= TypeObject "AcDbMText")
		(progn
			; Su dung vla-get-TextString de lay raw content
			(princ "\nUsing vla-get-TextString for MTEXT content...")
			(vl-catch-all-apply (function (lambda ()
				(setq StringTemp (vla-get-TextString VlaObject))
			)))

			; Neu khong lay duoc, fallback sang getpropertyvalue
			(if (not StringTemp)
				(setq StringTemp (getpropertyvalue (vlax-vla-object->ename VlaObject) "TEXT"))
			)

			; Xu ly string content
			(if StringTemp
				(setq ListStringContent (CAEX_STRING_TO_LIST_NEW StringTemp "\r\n"))
				(setq ListStringContent (list ""))
			)
		)
	)
	; Clean MTEXT format codes neu can
	(if (= TypeObject "AcDbMText")
		(setq ListStringContent (mapcar 'CLEAN-MTEXT ListStringContent))
	)
	ListStringContent
)

; Ham clean MTEXT format codes - Enhanced version
(defun CLEAN-MTEXT (text_string / cleaned_text pos end_pos)
	(if text_string
		(progn
			(setq cleaned_text text_string)

			; Loai bo cac format codes \xxx; (color, alignment, etc.)
			(while (vl-string-search "\\" cleaned_text)
				(setq pos (vl-string-search "\\" cleaned_text))
				(setq end_pos (vl-string-search ";" cleaned_text pos))
				(if end_pos
					(setq cleaned_text (strcat
						(substr cleaned_text 1 pos)
						(substr cleaned_text (+ end_pos 2))
					))
					; Neu khong co dau ; thi loai bo het phan con lai
					(setq cleaned_text (substr cleaned_text 1 pos))
				)
			)

			; Loai bo cac ky tu dac biet { va }
			(setq cleaned_text (vl-string-subst "" "{" cleaned_text))
			(setq cleaned_text (vl-string-subst "" "}" cleaned_text))

			; Loai bo cac format codes khac
			(setq cleaned_text (vl-string-subst "" "\\P" cleaned_text))  ; Paragraph break
			(setq cleaned_text (vl-string-subst "" "\\p" cleaned_text))  ; Paragraph break
			(setq cleaned_text (vl-string-subst "" "\\X" cleaned_text))  ; Cancel formatting
			(setq cleaned_text (vl-string-subst "" "\\x" cleaned_text))  ; Cancel formatting

			; Chuyen doi cac ky hieu dac biet
			(setq cleaned_text (vl-string-subst "Ø" "%%c" cleaned_text))  ; Diameter
			(setq cleaned_text (vl-string-subst "±" "%%p" cleaned_text))  ; Plus/minus
			(setq cleaned_text (vl-string-subst "°" "%%d" cleaned_text))  ; Degree
			(setq cleaned_text (vl-string-subst "%" "%%" cleaned_text))   ; Percent

			; Loai bo cac space thua
			(setq cleaned_text (vl-string-trim " \t\n\r" cleaned_text))
		)
		(setq cleaned_text "")
	)
	cleaned_text
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CONVERT_SELECTIONSET_TO_LISTVLAOBJECT ( SelectionSet /
	VlaObject
	ListVlaObject
	Num)

	(if SelectionSet
		(progn
			(setq Num 0)
			(repeat (sslength SelectionSet)
				(setq VlaObject (vlax-ename->vla-object (ssname SelectionSet Num)))
				(setq ListVlaObject (cons VlaObject ListVlaObject))
				(setq Num (+ Num 1))
			)
		)
	)
	ListVlaObject
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_RANDOM_STRING ( NumStringLength / m a c Temp RandomString)
	(setq m 4294967296.0)
	(setq a 1664525.0)
	(setq c 1013904223.0)
	(repeat NumStringLength
		(setq $xn (rem (+ c (* a (cond ($xn) ((getvar 'date))))) m))
		(setq Temp (fix (* (/ $xn m) 36)))
		(if (= Temp 0) (setq Temp "0"))
		(if (= Temp 1) (setq Temp "1"))
		(if (= Temp 2) (setq Temp "2"))
		(if (= Temp 3) (setq Temp "3"))
		(if (= Temp 4) (setq Temp "4"))
		(if (= Temp 5) (setq Temp "5"))
		(if (= Temp 6) (setq Temp "6"))
		(if (= Temp 7) (setq Temp "7"))
		(if (= Temp 8) (setq Temp "8"))
		(if (= Temp 9) (setq Temp "9"))
		(if (= Temp 10) (setq Temp "A"))
		(if (= Temp 11) (setq Temp "B"))
		(if (= Temp 12) (setq Temp "C"))
		(if (= Temp 13) (setq Temp "D"))
		(if (= Temp 14) (setq Temp "E"))
		(if (= Temp 15) (setq Temp "F"))
		(if (= Temp 16) (setq Temp "G"))
		(if (= Temp 17) (setq Temp "H"))
		(if (= Temp 18) (setq Temp "I"))
		(if (= Temp 19) (setq Temp "J"))
		(if (= Temp 20) (setq Temp "K"))
		(if (= Temp 21) (setq Temp "L"))
		(if (= Temp 22) (setq Temp "M"))
		(if (= Temp 23) (setq Temp "N"))
		(if (= Temp 24) (setq Temp "O"))
		(if (= Temp 25) (setq Temp "P"))
		(if (= Temp 26) (setq Temp "Q"))
		(if (= Temp 27) (setq Temp "R"))
		(if (= Temp 28) (setq Temp "S"))
		(if (= Temp 29) (setq Temp "T"))
		(if (= Temp 30) (setq Temp "U"))
		(if (= Temp 31) (setq Temp "V"))
		(if (= Temp 32) (setq Temp "W"))
		(if (= Temp 33) (setq Temp "X"))
		(if (= Temp 34) (setq Temp "Y"))
		(if (= Temp 35) (setq Temp "Z"))
		(if RandomString
			(setq RandomString (strcat Temp RandomString))
			(setq RandomString Temp)
		)
	)
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_ROUNDOFF_NUMBER ( Number ValueRoundOff /
	Temp1
	Temp2
	ValueResult
	NumberActive
	NumSwitchPositveActive)

	(setq NumberActive (abs (* Number 1.0)))
	(if (>= Number 0.0)
		(setq NumSwitchPositveActive 1.0)
		(setq NumSwitchPositveActive -1.0)
	)
	(setq Temp1 (/ NumberActive ValueRoundOff))
	(setq Temp2 (fix Temp1))
	(if (>= (abs (- Temp1 Temp2)) 0.4999999999)
		(setq ValueResult (* ValueRoundOff (+ Temp2 1.0)))
		(setq ValueResult (* ValueRoundOff (+ Temp2 0.0)))
	)
	(setq ValueResult (* NumSwitchPositveActive ValueResult))
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_STRING_TO_LIST_NEW (Stg Del / ListString)
	(setq ListString (CAEX_STRING_TO_LIST_NO_TRIM Stg Del))
	(setq ListString (mapcar '(lambda (x) (vl-string-trim " " x)) ListString))
	(setq ListString (mapcar '(lambda (x) (vl-string-trim "\t" x)) ListString))
	ListString
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_STRING_TO_LIST_NO_TRIM (Stg Del / LenDel StgTemp Pos StgSub StgSubTemp ListString)
	(if Stg
		(progn
			(setq LenDel (strlen Del))
			(setq StgTemp Stg)
			(while (setq Pos (vl-string-search Del StgTemp))
				(setq StgSub (substr StgTemp 1 Pos))
				(setq StgTemp (substr StgTemp (+ Pos 1 LenDel)))
				(setq StgSubTemp StgSub)
				(if (/= StgSubTemp "")
					(setq ListString (cons StgSub ListString))
				)
			)
			(setq StgSub StgTemp)
			(setq StgSubTemp StgSub)

			(if (/= StgSubTemp "")
				(setq ListString (cons StgSub ListString))
			)
			(if (not ListString)
				(setq ListString (list Stg))
			)
			(setq ListString (reverse ListString))
		)
	)
	ListString
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_LIST_TO_STRING ( ListString Sep / StringValue)
	(setq StringValue (car ListString))
	(foreach StringTemp (cdr ListString)
		(setq StringValue (strcat StringValue Sep StringTemp))
	)
	StringValue
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_FIND_ALIGNMENT_TEXT_MTEXT ( VlaObject / 
	Alignment
	ListDataAttachment
	TypeObject)

	(setq TypeObject (vla-get-ObjectName VlaObject))
	(if (= TypeObject "AcDbMText")
		(progn
			(setq ListDataAttachment
				(list
					(cons acAttachmentPointTopLeft "Left")
					(cons acAttachmentPointTopCenter "Center")
					(cons acAttachmentPointTopRight "Right")
					(cons acAttachmentPointMiddleLeft "Left")
					(cons acAttachmentPointMiddleCenter "Center")
					(cons acAttachmentPointMiddleRight "Right")
					(cons acAttachmentPointBottomLeft "Left")
					(cons acAttachmentPointBottomCenter "Center")
					(cons acAttachmentPointBottomRight "Right")
				)
			)
			(setq Alignment (vla-get-AttachmentPoint VlaObject))
			(setq Alignment (cdr (assoc Alignment ListDataAttachment)))
		)
	)
	(if (= TypeObject "AcDbText")
		(progn
			(setq ListDataAttachment
				(list
					(cons acAlignmentLeft "Left")
					(cons acAlignmentCenter "Center")
					(cons acAlignmentRight "Right")
					(cons acAlignmentAligned "Center")
					(cons acAlignmentMiddle "Center")
					(cons acAlignmentFit "Center")
					(cons acAlignmentTopLeft "Left")
					(cons acAlignmentTopCenter "Center")
					(cons acAlignmentTopRight "Right")
					(cons acAlignmentMiddleLeft "Left")
					(cons acAlignmentMiddleCenter "Center")
					(cons acAlignmentMiddleRight "Right")
					(cons acAlignmentBottomLeft "Left")
					(cons acAlignmentBottomCenter "Center")
					(cons acAlignmentBottomRight "Right")
				)
			)
			(setq Alignment (vla-get-Alignment VlaObject))
			(setq Alignment (cdr (assoc Alignment ListDataAttachment)))
		)
	)
	Alignment
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_FIND_VALUE_POPULAR ( ListValue / ListData )
	(foreach Value ListValue
		(if (setq Temp (assoc Value ListData))
			(setq ListData (subst (cons Value (+ (cdr Temp) 1)) Temp ListData))
			(setq ListData (cons (cons Value 1) ListData))
		)
	)
	(setq ListData (vl-sort ListData '(lambda (a b) (> (cdr a) (cdr b)))))
	(car (car ListData))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_VLA_GET_STYLENAME ( VlaObject /
	NameStyle
	TypeObject
	DataEname
	NumCode
	NameStyle
	DataEname
	DataEnameTemp)

	(vl-catch-all-apply (function (lambda ( / )
		(setq NameStyle (vla-get-stylename VlaObject))
	)))
	(if
		(or
			(not NameStyle)
			(and
				NameStyle
				(vl-string-search "?" NameStyle)
			)
		)
		(progn
			(setq TypeObject (vla-get-ObjectName VlaObject))
			(if
				(or
					(= TypeObject "AcDb2LineAngularDimension")
					(= TypeObject "AcDb3PointAngularDimension")
					(= TypeObject "AcDbAlignedDimension")
					(= TypeObject "AcDbArcDimension")
					(= TypeObject "AcDbDiametricDimension")
					(= TypeObject "AcDbFcf")
					(= TypeObject "AcDbLeader")
					(= TypeObject "AcDbOrdinateDimension")
					(= TypeObject "AcDbRadialDimension")
					(= TypeObject "AcDbRadialDimensionLarge")
					(= TypeObject "AcDbRotatedDimension")
				)
				(setq NameStyle (cdr (assoc 3 (entget (vlax-vla-object->ename VlaObject)))))
			)
			(if
				(or
					(= TypeObject "AcDbAttributeDefinition")
					(= TypeObject "AcDbMText")
					(= TypeObject "AcDbText")
					(= TypeObject "AcDbAttribute")
				)
				(setq NameStyle (cdr (assoc 7 (entget (vlax-vla-object->ename VlaObject)))))
			)
			(if (= TypeObject "AcDbMline")
				(setq NameStyle (cdr (assoc 2 (entget (vlax-vla-object->ename VlaObject)))))
			)
			(if (= TypeObject "AcDbMLeader")
				(progn
					(setq DataEname (entget (vlax-vla-object->ename VlaObject)))
					(setq NumCode 340)
					(setq NameStyle Nil)
					(while (and (assoc NumCode DataEname) (not NameStyle))
						(setq DataEnameTemp (entget (cdr (assoc NumCode DataEname))))
						(if
							(= (cdr (assoc 0 DataEnameTemp)) "MLEADERSTYLE")
							(setq NameStyle (CAEX_VLA_GET_NAME (vlax-ename->vla-object (cdr (assoc NumCode DataEname)))))
						)
						(setq DataEname (vl-remove (assoc NumCode DataEname) DataEname))
					)
				)
			)
			(if (= TypeObject "AcDbTable")
				(progn
					(setq DataEname (entget (vlax-vla-object->ename VlaObject)))
					(setq NumCode 342)
					(setq NameStyle Nil)
					(while (and (assoc NumCode DataEname) (not NameStyle))
						(setq DataEnameTemp (entget (cdr (assoc NumCode DataEname))))
						(if
							(= (cdr (assoc 0 DataEnameTemp)) "TABLESTYLE")
							(setq NameStyle (CAEX_VLA_GET_NAME (vlax-ename->vla-object (cdr (assoc NumCode DataEname)))))
						)
						(setq DataEname (vl-remove (assoc NumCode DataEname) DataEname))
					)
				)
			)
		)
	)
	NameStyle
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_LISTSTRING_TO_FORMULAR_EXCEL ( ListString / StringResult)
	(setq ListString (mapcar 'CAEX_TRANSLATE_SEPARATOR_EXCEL ListString))
	(setq StringResult (strcat "=" (CAEX_LIST_TO_STRING ListString "&CHAR(10)&")))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_TRANSLATE_SEPARATOR_EXCEL ( String /
	ListTemp
	StringResult)

	(setq ListTemp (CAEX_STRING_TO_LIST_NO_TRIM String SeparatorCSV))
	(setq ListTemp (mapcar 'CAEX_TRANSLATE_UNICODE_EXCEL ListTemp))
	(setq StringResult (CAEX_LIST_TO_STRING ListTemp (strcat "&CHAR(" (itoa (car (vl-string->list SeparatorCSV))) ")&")))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_TRANSLATE_UNICODE_EXCEL ( String /
	NumLength
	StringFormular
	ListTemp
	Temp1
	Temp2)
	
	(setq String (CAEX_CONVERT_STRING_TO_UNICODE String))
	(setq String (strcat "*" String))
	(setq ListTemp (CAEX_STRING_TO_LIST_NO_TRIM String "\\U+"))
	(setq Temp1 (car ListTemp))
	(setq Temp2 (substr Temp1 2))
	(setq StringFormular (strcat "\"" Temp2 "\""))

	(foreach Temp1 (cdr ListTemp)
		(if (< (strlen Temp1) 4)
			(setq StringFormular (strcat StringFormular "&" "\"" Temp1 "\""))
			(progn
				(setq Temp2
					(strcat
						"UNICHAR(HEX2DEC(\""
						(substr Temp1 1 4)
						"\"))"
					)
				)
				(setq StringFormular (strcat StringFormular "&" Temp2))
				(setq Temp2 (substr Temp1 5))
				(if (/= Temp2 "")
					(setq StringFormular (strcat StringFormular "&" "\"" Temp2 "\""))
				)
			)
		)
	)
	StringFormular
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_CONVERT_STRING_TO_UNICODE ( StringInput / StringResult)
	(setq StringResult
		(apply 'strcat
			(mapcar
				'(lambda ( x / s )
					(if (<= x 121)
						(setq s (chr x))
						(progn
							(setq s (CAEX_CONVERT_DECIMAL_TO_BASE x 16))
							(repeat (- 4 (strlen s))
								(setq s (strcat "0" s))
							)
							(setq s (strcat "\\U+" s))
						)
					)
					s
				)
				(vl-string->list StringInput)
			)
		)
	)
	StringResult
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_CONVERT_DECIMAL_TO_BASE ( Num Base / 
	CharTemp
	NumMod
	StringNum)

	(setq StringNum "")
	(while (> Num 0)
		(setq NumMod (rem Num Base))
		(if (< NumMod 10)
			(setq CharTemp (chr (+ NumMod 48)))
			(setq CharTemp (chr (+ NumMod 55)))
		)
		(setq StringNum (strcat CharTemp StringNum))
		(setq Num (/ Num Base))
	)
	(if (= StringNum "")
		(setq StringNum "0")
	)
	StringNum
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CHECK_SAME_POINT ( Point1 Point2 ToleranceValue / DistanceLength)
	(setq DistanceLength (distance Point1 Point2))
	(equal DistanceLength 0.0 ToleranceValue)
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_CHECK_FILE_EXIST ( File /
	FileResult
	VlaFileSystem 
	VlaFile)

	(setq VlaFileSystem (vla-getinterfaceobject (vlax-get-acad-object) "Scripting.FileSystemObject"))
	(if (= (vlax-invoke-method VlaFileSystem "FileExists" File) :vlax-true)
		(progn
			(setq VlaFile (vlax-invoke-method VlaFileSystem "GetFile" File))
			(setq FileResult (vlax-get-property VlaFile "Path"))
		)
	)
	FileResult
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_PROPERTIES_OBJECT ( VlaObject /
	ListNameProperty
	ListPropertyObject)

	(setq ListNameProperty
		(list
			"EntityTransparency"
			"Layer"
			"Linetype"
			"LinetypeScale"
			"Lineweight"
			"Material"
			"PlotStyleName"
			"TrueColor"
		)
	)
	(foreach NameProperty ListNameProperty
		(vl-catch-all-error-p (vl-catch-all-apply (function (lambda ( / )
			(setq ListPropertyObject (cons (cons NameProperty (vlax-get-property VlaObject NameProperty)) ListPropertyObject))
		))))
	)
	ListPropertyObject
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_PUT_PROPERTIES_OBJECT ( VlaObject ListPropertyObject /
	NameProperty
	ValueProperty)

	(foreach PropertyObject ListPropertyObject
		(vl-catch-all-error-p (vl-catch-all-apply (function (lambda ( / )
			(setq NameProperty (car PropertyObject))
			(setq ValueProperty (cdr PropertyObject))
			(vlax-put-property VlaObject NameProperty ValueProperty)
		))))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CREATE_POLYLINE ( DataPolyline CheckClosed /
	ListBulge
	ListPoint 
	ListCoordinates
	Num
	VlaObjectPolyline)

	(setq ListPoint (nth 0 DataPolyline))
	(setq ListBulge (nth 1 DataPolyline))
	(setq ListCoordinates (apply 'append (mapcar '(lambda (x) (list (nth 0 x) (nth 1 x))) ListPoint)))
	(setq VlaObjectPolyline
		(vla-AddLightWeightPolyline
			VlaSpace
			(vlax-safearray-fill
				(vlax-make-safearray
					vlax-vbDouble
					(cons 0 (- (length ListCoordinates) 1))
				)
				ListCoordinates
			)
		)
	)
	(setq Num 0)
	(repeat (length ListBulge)
		(setq Bulge (nth Num ListBulge))
		(vla-SetBulge VlaObjectPolyline Num Bulge)
		(setq Num (+ Num 1))
	)
	(if CheckClosed
		(vla-put-closed VlaObjectPolyline :vlax-true)
	)
	VlaObjectPolyline
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CREATE_LISTVLALAYERLOCK ( / VlaLayersGroup)
	(setq VlaLayersGroup (vla-get-layers VlaDrawingCurrent))
	(vlax-for VlaLayer VlaLayersGroup
		(if
			(= (vla-get-Lock VlaLayer) :vlax-true)
			(progn
				(vla-put-Lock VlaLayer :vlax-false)
				(setq ListVlaLayerLock (cons VlaLayer ListVlaLayerLock))
			)
		)
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_RESTORE_LOCK_LAYER ( / )
	(foreach VlaLayerLock ListVlaLayerLock
		(vl-catch-all-error-p (vl-catch-all-apply 'vla-put-Lock (list VlaLayerLock :vlax-true)))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_SET_VARSYSTEM_C2E ( / Temp VarSystem)
	(foreach Temp (list (list "CMDECHO" 0) (list "DIMZIN" 8) (list "MODEMACRO" "Export table in cad to excel..."))
		(setq VarSystem (car Temp))
		(setq ListVarSystem_OldValue (cons (list VarSystem (getvar VarSystem)) ListVarSystem_OldValue))
		(setvar VarSystem (cadr Temp))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_SET_VARSYSTEM_E2C ( / Temp VarSystem)
	(foreach Temp (list (list "CMDECHO" 0) (list "DIMZIN" 8) (list "MODEMACRO" "Export table in excel to cad..."))
		(setq VarSystem (car Temp))
		(setq ListVarSystem_OldValue (cons (list VarSystem (getvar VarSystem)) ListVarSystem_OldValue))
		(setvar VarSystem (cadr Temp))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_SET_VARSYSTEM_CUE ( / Temp VarSystem)
	(foreach Temp (list (list "CMDECHO" 0) (list "DIMZIN" 8) (list "MODEMACRO" "Update table in cad from excel..."))
		(setq VarSystem (car Temp))
		(setq ListVarSystem_OldValue (cons (list VarSystem (getvar VarSystem)) ListVarSystem_OldValue))
		(setvar VarSystem (cadr Temp))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_SET_VARSYSTEM_EUC ( / Temp VarSystem)
	(foreach Temp (list (list "CMDECHO" 0) (list "DIMZIN" 8) (list "MODEMACRO" "Update table in excel from cad..."))
		(setq VarSystem (car Temp))
		(setq ListVarSystem_OldValue (cons (list VarSystem (getvar VarSystem)) ListVarSystem_OldValue))
		(setvar VarSystem (cadr Temp))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_RESET_VARSYSTEM ( / Temp VarSystem)
	(foreach Temp ListVarSystem_OldValue
		(setvar (car Temp) (cadr Temp))
	)
)

; ===================================================================
; CAC LENH MOI THEO CU PHAP MOI
; ===================================================================

; ===================================================================
; NHOM E1 - XUAT CELL - CAC LENH MOI
; ===================================================================

; E14 - DAT NHANH HE SO (FACTOR)=1
(defun C:E14 ( / )
	(setq *E6-factor* "1")
	(princ "\nDa dat he so factor = 1")
	(princ)
)

; E15 - DAT NHANH HE SO (FACTOR)=0.001
(defun C:E15 ( / )
	(setq *E6-factor* "0.001")
	(princ "\nDa dat he so factor = 0.001")
	(princ)
)

; ===================================================================
; NHOM E2 - XUAT COL, ROW, ARRAY - CAC LENH MOI
; ===================================================================

; E21 - XUAT NHANH RA COT (VI TRI CHUOT TU DONG NHAY XUONG O CUOI CUNG CUA COT)
(defun C:E21 ( / ActDoc *Space* xlapp xlcells startrow startcol otcontents oerror)
	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Thuc hien xuat cot
			(ET-EXECUTE-COL otcontents xlcells startrow startcol)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
)

; E22 - XUAT NHANH RA HANG (VI TRI CHUOT TU DONG NHAY QUA PHAI O CUOI CUNG CUA HANG)
(defun C:E22 ( / ActDoc *Space* xlapp xlcells startrow startcol otcontents oerror)
	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Thuc hien xuat hang
			(ET-EXECUTE-ROW otcontents xlcells startrow startcol)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
)

; E23 - XUAT NHANH RA MANG (VI TRI CHUOT TU DONG NHAY XUONG O CUOI CUNG CUA MANG)
(defun C:E23 ( / ActDoc *Space* xlapp xlcells startrow startcol otcontents oerror)
	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Thuc hien xuat mang
			(ET-EXECUTE-ARRAY otcontents xlcells startrow startcol)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
)

; E24 - BAT NHANH NUMBER (Y)
(defun C:E24 ( / )
	(setq *E6-number* "Y")
	(princ "\nDa bat Number = Y")
	(princ "\nE2 se chi lay so tu text (nhu E1)")
	(princ)
)

; E25 - TAT NHANH NUMBER (N)
(defun C:E25 ( / )
	(setq *E6-number* "N")
	(princ "\nDa tat Number = N")
	(princ "\nE2 se giu nguyen text nhu hien tai")
	(princ)
)



; ===================================================================
; NHOM E3 - GHI HANDLE - CAC LENH MOI
; ===================================================================

; E31 - MO FILE CAD THEO COMMENT (THAY CHO HTC CU)
(defun C:E31 ( / )
	; Goi ham HTC cu
	(C:HTC)
	(princ)
)

; E32 - ZOOM VA HIGHLIGHT DOI TUONG THEO HANDLE (THAY CHO ZTH CU)
(defun C:E32 ( / )
	; Goi ham ZTH cu
	(C:ZTH)
	(princ)
)

; E33 - ZOOM VA SELECT DOI TUONG THEO HANDLE (THAY CHO STH CU)
(defun C:E33 ( / )
	; Goi ham STH cu
	(C:STH)
	(princ)
)

; E34 - BAT NHANH HANDLE (GAN CHE DO THIET LAP HANDLE Y)
(defun C:E34 ( / )
	(setq *E6-handle* "Y")
	(princ "\nDa bat Handle = Y")
	(princ "\nCac lenh E1, E11, E12, E2, E3, E4, ET se ghi handle vao comment")
	(princ)
)

; E35 - TAT NHANH HANDLE (GAN CHE DO THIET LAP HANDLE N)
(defun C:E35 ( / )
	(setq *E6-handle* "N")
	(princ "\nDa tat Handle = N")
	(princ "\nCac lenh E1, E11, E12, E2, E3, E4, ET se KHONG ghi handle vao comment")
	(princ)
)

; ===================================================================
; NHOM E4 - XUAT TABLE - CAC LENH MOI
; ===================================================================

; E41 - XUAT BANG THEO LINE (GIU FORMAT BANG) - THAY CHO CTE CU
(defun C:E41 ( / )
	; Goi ham CTE cu
	(C:CTE)
	(princ)
)

; E42 - XUAT BANG THEO TABLE - THAY CHO TE CU
(defun C:E42 ( / )
	; Goi ham TE cu
	(C:TE)
	(princ)
)

; E43 - XUAT BANG TU EXCEL QUA CAD - THAY CHO ETC CU
(defun C:E43 ( / )
	; Goi ham ETC cu
	(C:ETC)
	(princ)
)

; E44 - CONG TAC MO FRAME (Y/N)
(defun C:E44 ( / )
	; Dao trang thai Frame
	(if (= *E6-frame* "Y")
		(progn
			(setq *E6-frame* "N")
			(princ "\n=== FRAME: TAT ===")
			(princ "\nCac lenh E4, E41, E42 se KHONG dong khung cho bang")
		)
		(progn
			(setq *E6-frame* "Y")
			(princ "\n=== FRAME: MO ===")
			(princ "\nCac lenh E4, E41, E42 se dong khung cho bang")
		)
	)
	(princ (strcat "\nTrang thai hien tai: Frame = " *E6-frame*))
	(princ)
)

; ===================================================================
; LENH HELP VA THONG BAO
; ===================================================================

(defun SHOW-NEW-SYNTAX-INFO ( / )
	(princ "\n=== THONG BAO CU PHAP MOI ===")
	(princ "\nFile da duoc cap nhat cu phap moi:")
	(princ "\n")
	(princ "\nNhom E1 (Xuat cell):")
	(princ "\n  E1, E11 - giu nguyen")
	(princ "\n  E12 - thay cho E2 cu")
	(princ "\n  E14 - dat nhanh factor=1")
	(princ "\n  E15 - dat nhanh factor=0.001")
	(princ "\n")
	(princ "\nNhom E2 (Xuat Col/Row/Array):")
	(princ "\n  E2 - thay cho E3 cu")
	(princ "\n  E21 - xuat nhanh ra cot")
	(princ "\n  E22 - xuat nhanh ra hang")
	(princ "\n  E23 - xuat nhanh ra mang")
	(princ "\n  E24 - bat nhanh Number")
	(princ "\n  E25 - tat nhanh Number")
	(princ "\n")
	(princ "\nNhom E3 (Ghi Handle):")
	(princ "\n  E3 - thay cho E4 cu")
	(princ "\n  E31 - thay cho HTC cu")
	(princ "\n  E32 - thay cho ZTH cu")
	(princ "\n  E33 - thay cho STH cu")
	(princ "\n  E34 - bat nhanh handle")
	(princ "\n  E35 - tat nhanh handle")
	(princ "\n")
	(princ "\nNhom E4 (Xuat Table):")
	(princ "\n  E4 - thay cho E5 cu")
	(princ "\n  E41 - thay cho CTE cu")
	(princ "\n  E42 - thay cho TE cu")
	(princ "\n  E43 - thay cho ETC cu")
	(princ "\n  E44 - cong tac Frame")
	(princ "\n")
	(princ "\nKhac:")
	(princ "\n  E0 - thay cho E6 cu (thiet lap)")
	(princ "\n  E7, E8, E9, ET - giu nguyen")
	(princ "\n")
	(princ "\n=== KET THUC THONG BAO ===")
	(princ)
)

; LENH HELP
(defun C:HELP-NEW-SYNTAX ( / )
	(SHOW-NEW-SYNTAX-INFO)
)

; Hien thi thong bao khi load file
(princ "\n=== Z-CAD2EXCEL-E_ALL.LSP - PHIEN BAN TICH HOP CU PHAP MOI ===")
(princ "\nFile da duoc tich hop thanh cong voi cu phap moi!")
(princ "\nSu dung lenh HELP-NEW-SYNTAX de xem huong dan chi tiet.")
(princ "\nTat ca cac lenh cu va moi deu co the su dung trong 1 file duy nhat.")
(princ)
